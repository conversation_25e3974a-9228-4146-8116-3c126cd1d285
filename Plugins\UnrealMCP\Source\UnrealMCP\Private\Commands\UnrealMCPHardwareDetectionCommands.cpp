// Copyright Epic Games, Inc. All Rights Reserved.

#include "Commands/UnrealMCPHardwareDetectionCommands.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "Engine/World.h"
#include "RenderCore.h"
#include "RendererInterface.h"
#include "HAL/PlatformApplicationMisc.h"
#include "Misc/DateTime.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "HAL/PlatformMemory.h"
#include "Stats/Stats.h"
#include "RenderingThread.h"

// Initialize static constants
const TArray<FString> UnrealMCPHardwareDetectionCommands::SupportedBenchmarkTypes = {
    TEXT("Full"),
    TEXT("Quick"),
    TEXT("GPU"),
    TEXT("CPU"),
    TEXT("Memory"),
    TEXT("Storage")
};

const TArray<FString> UnrealMCPHardwareDetectionCommands::SupportedHardwareTiers = {
    TEXT("HighEnd"),
    TEXT("MidRange"),
    TEXT("LowEnd"),
    TEXT("Mobile"),
    TEXT("Auto")
};

const TArray<FString> UnrealMCPHardwareDetectionCommands::SupportedQualityPresets = {
    TEXT("Performance"),
    TEXT("Balanced"),
    TEXT("Quality"),
    TEXT("Ultra")
};

const TArray<FString> UnrealMCPHardwareDetectionCommands::SupportedOutputFormats = {
    TEXT("JSON"),
    TEXT("HTML"),
    TEXT("PDF"),
    TEXT("CSV"),
    TEXT("XML")
};

const TArray<FString> UnrealMCPHardwareDetectionCommands::SupportedMemoryBuckets = {
    TEXT("Largest"),
    TEXT("Larger"),
    TEXT("Default"),
    TEXT("Smaller"),
    TEXT("Smallest"),
    TEXT("Auto")
};

const TArray<FString> UnrealMCPHardwareDetectionCommands::SupportedGPUFamilies = {
    TEXT("NVIDIA_RTX"),
    TEXT("NVIDIA_GTX"),
    TEXT("AMD_RDNA"),
    TEXT("AMD_GCN"),
    TEXT("Intel_Arc"),
    TEXT("Intel_Integrated"),
    TEXT("Mali"),
    TEXT("Adreno"),
    TEXT("PowerVR"),
    TEXT("Auto")
};

const TArray<FString> UnrealMCPHardwareDetectionCommands::SupportedPlatformTypes = {
    TEXT("Windows"),
    TEXT("Mac"),
    TEXT("Linux"),
    TEXT("Android"),
    TEXT("iOS"),
    TEXT("PlayStation5"),
    TEXT("XboxSeriesX"),
    TEXT("Switch"),
    TEXT("Auto")
};

const int32 UnrealMCPHardwareDetectionCommands::DefaultBenchmarkDuration = 30;
const int32 UnrealMCPHardwareDetectionCommands::MaxBenchmarkDuration = 300;
const float UnrealMCPHardwareDetectionCommands::DefaultMonitoringDuration = 60.0f;
const float UnrealMCPHardwareDetectionCommands::MaxMonitoringDuration = 3600.0f;

UnrealMCPHardwareDetectionCommands::UnrealMCPHardwareDetectionCommands()
{
}

UnrealMCPHardwareDetectionCommands::~UnrealMCPHardwareDetectionCommands()
{
}

FString UnrealMCPHardwareDetectionCommands::HandleDetectSystemHardware(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject = ParseJsonParams(JsonParams);
    if (!JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    bool bIncludeCPUInfo = true;
    JsonObject->TryGetBoolField(TEXT("include_cpu_info"), bIncludeCPUInfo);

    bool bIncludeGPUInfo = true;
    JsonObject->TryGetBoolField(TEXT("include_gpu_info"), bIncludeGPUInfo);

    bool bIncludeMemoryInfo = true;
    JsonObject->TryGetBoolField(TEXT("include_memory_info"), bIncludeMemoryInfo);

    bool bIncludeStorageInfo = true;
    JsonObject->TryGetBoolField(TEXT("include_storage_info"), bIncludeStorageInfo);

    bool bDetailedAnalysis = false;
    JsonObject->TryGetBoolField(TEXT("detailed_analysis"), bDetailedAnalysis);

    // Create hardware detection result
    TSharedPtr<FJsonObject> HardwareInfo = MakeShareable(new FJsonObject);

    // Detect CPU information
    if (bIncludeCPUInfo)
    {
        TSharedPtr<FJsonObject> CPUInfo = DetectCPUInformation();
        if (CPUInfo.IsValid())
        {
            HardwareInfo->SetObjectField(TEXT("cpu"), CPUInfo);
        }
    }

    // Detect GPU information
    if (bIncludeGPUInfo)
    {
        TSharedPtr<FJsonObject> GPUInfo = DetectGPUInformation();
        if (GPUInfo.IsValid())
        {
            HardwareInfo->SetObjectField(TEXT("gpu"), GPUInfo);
        }
    }

    // Detect memory information
    if (bIncludeMemoryInfo)
    {
        TSharedPtr<FJsonObject> MemoryInfo = DetectMemoryInformation();
        if (MemoryInfo.IsValid())
        {
            HardwareInfo->SetObjectField(TEXT("memory"), MemoryInfo);
        }
    }

    // Detect storage information
    if (bIncludeStorageInfo)
    {
        TSharedPtr<FJsonObject> StorageInfo = DetectStorageInformation();
        if (StorageInfo.IsValid())
        {
            HardwareInfo->SetObjectField(TEXT("storage"), StorageInfo);
        }
    }

    // Add system information
    TSharedPtr<FJsonObject> SystemInfo = DetectSystemInformation();
    if (SystemInfo.IsValid())
    {
        HardwareInfo->SetObjectField(TEXT("system"), SystemInfo);
    }

    // Add detection timestamp
    HardwareInfo->SetStringField(TEXT("detection_timestamp"), FDateTime::Now().ToString());
    HardwareInfo->SetBoolField(TEXT("detailed_analysis"), bDetailedAnalysis);

    return CreateJsonResponse(true, TEXT("System hardware detected successfully"), HardwareInfo);
}

FString UnrealMCPHardwareDetectionCommands::HandleRunHardwareBenchmark(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject = ParseJsonParams(JsonParams);
    if (!JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString BenchmarkType = TEXT("Full");
    JsonObject->TryGetStringField(TEXT("benchmark_type"), BenchmarkType);

    int32 Duration = DefaultBenchmarkDuration;
    JsonObject->TryGetNumberField(TEXT("duration_seconds"), Duration);

    bool bApplyResults = true;
    JsonObject->TryGetBoolField(TEXT("apply_results"), bApplyResults);

    bool bSaveResults = true;
    JsonObject->TryGetBoolField(TEXT("save_results"), bSaveResults);

    FString OutputFormat = TEXT("JSON");
    JsonObject->TryGetStringField(TEXT("output_format"), OutputFormat);

    // Validate parameters
    if (!ValidateBenchmarkType(BenchmarkType))
    {
        return CreateJsonResponse(false, FString::Printf(TEXT("Unsupported benchmark type: %s"), *BenchmarkType));
    }

    if (Duration > MaxBenchmarkDuration)
    {
        Duration = MaxBenchmarkDuration;
    }

    // Run benchmark based on type
    TSharedPtr<FJsonObject> BenchmarkResults;
    
    if (BenchmarkType == TEXT("Full"))
    {
        BenchmarkResults = RunFullSystemBenchmark(Duration);
    }
    else if (BenchmarkType == TEXT("GPU"))
    {
        BenchmarkResults = RunGPUBenchmark(Duration);
    }
    else if (BenchmarkType == TEXT("CPU"))
    {
        BenchmarkResults = RunCPUBenchmark(Duration);
    }
    else if (BenchmarkType == TEXT("Memory"))
    {
        BenchmarkResults = RunMemoryBenchmark(Duration);
    }
    else if (BenchmarkType == TEXT("Quick"))
    {
        BenchmarkResults = RunFullSystemBenchmark(Duration / 3); // Quick benchmark
    }

    if (!BenchmarkResults.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Failed to run hardware benchmark"));
    }

    // Apply results if requested
    if (bApplyResults)
    {
        if (!ApplyBenchmarkResults(BenchmarkResults))
        {
            return CreateJsonResponse(false, TEXT("Failed to apply benchmark results"));
        }
    }

    // Save results if requested
    if (bSaveResults)
    {
        FString Filename = FString::Printf(TEXT("HardwareBenchmark_%s_%s"), 
            *BenchmarkType, *FDateTime::Now().ToString(TEXT("%Y%m%d_%H%M%S")));
        SaveReportToFile(BenchmarkResults, OutputFormat, Filename);
    }

    // Add benchmark metadata
    BenchmarkResults->SetStringField(TEXT("benchmark_type"), BenchmarkType);
    BenchmarkResults->SetNumberField(TEXT("duration_seconds"), Duration);
    BenchmarkResults->SetBoolField(TEXT("results_applied"), bApplyResults);
    BenchmarkResults->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    return CreateJsonResponse(true, TEXT("Hardware benchmark completed successfully"), BenchmarkResults);
}

// Helper function implementations
TSharedPtr<FJsonObject> UnrealMCPHardwareDetectionCommands::DetectCPUInformation()
{
    TSharedPtr<FJsonObject> CPUInfo = MakeShareable(new FJsonObject);

    // Get CPU brand string
    FString CPUBrand = GetCPUBrandString();
    CPUInfo->SetStringField(TEXT("brand"), CPUBrand);

    // Get CPU core count
    int32 CoreCount = FPlatformMisc::NumberOfCores();
    CPUInfo->SetNumberField(TEXT("core_count"), CoreCount);

    // Get CPU core count including hyperthreading
    int32 LogicalCoreCount = FPlatformMisc::NumberOfCoresIncludingHyperthreads();
    CPUInfo->SetNumberField(TEXT("logical_core_count"), LogicalCoreCount);

    // Get CPU architecture
    FString CPUArchitecture = FPlatformMisc::GetCPUVendor();
    CPUInfo->SetStringField(TEXT("architecture"), CPUArchitecture);

    // Get CPU features
    TArray<FString> CPUFeatures;
    if (FPlatformMisc::HasNonoptionalCPUFeatures())
    {
        CPUFeatures.Add(TEXT("SSE"));
    }

    TArray<TSharedPtr<FJsonValue>> FeaturesArray;
    for (const FString& Feature : CPUFeatures)
    {
        FeaturesArray.Add(MakeShareable(new FJsonValueString(Feature)));
    }
    CPUInfo->SetArrayField(TEXT("features"), FeaturesArray);

    return CPUInfo;
}

TSharedPtr<FJsonObject> UnrealMCPHardwareDetectionCommands::DetectGPUInformation()
{
    TSharedPtr<FJsonObject> GPUInfo = MakeShareable(new FJsonObject);

    // Get GPU adapter name
    FString GPUName = GetGPUAdapterName();
    GPUInfo->SetStringField(TEXT("name"), GPUName);

    // Get RHI name
    FString RHIName = GDynamicRHI ? GDynamicRHI->GetName() : TEXT("Unknown");
    GPUInfo->SetStringField(TEXT("rhi"), RHIName);

    // Get feature level
    ERHIFeatureLevel::Type FeatureLevel = GetMaxSupportedFeatureLevel();
    FString FeatureLevelString = LexToString(FeatureLevel);
    GPUInfo->SetStringField(TEXT("feature_level"), FeatureLevelString);

    // Get shader platform
    EShaderPlatform ShaderPlatform = GMaxRHIShaderPlatform;
    FString ShaderPlatformString = LexToString(ShaderPlatform);
    GPUInfo->SetStringField(TEXT("shader_platform"), ShaderPlatformString);

    // Check ray tracing support
    bool bRayTracingSupported = IsRayTracingSupported();
    GPUInfo->SetBoolField(TEXT("ray_tracing_supported"), bRayTracingSupported);

    // Check API support
    GPUInfo->SetBoolField(TEXT("vulkan_supported"), IsVulkanSupported());
    GPUInfo->SetBoolField(TEXT("directx12_supported"), IsDirectX12Supported());

    return GPUInfo;
}

TSharedPtr<FJsonObject> UnrealMCPHardwareDetectionCommands::DetectMemoryInformation()
{
    TSharedPtr<FJsonObject> MemoryInfo = MakeShareable(new FJsonObject);

    // Get total physical memory
    int64 TotalPhysicalMemory = GetTotalPhysicalMemory();
    MemoryInfo->SetNumberField(TEXT("total_physical_gb"), TotalPhysicalMemory / (1024 * 1024 * 1024));

    // Get available physical memory
    int64 AvailablePhysicalMemory = GetAvailablePhysicalMemory();
    MemoryInfo->SetNumberField(TEXT("available_physical_gb"), AvailablePhysicalMemory / (1024 * 1024 * 1024));

    // Get memory stats
    FPlatformMemoryStats MemoryStats = FPlatformMemory::GetStats();
    MemoryInfo->SetNumberField(TEXT("used_physical_gb"), MemoryStats.UsedPhysical / (1024 * 1024 * 1024));
    MemoryInfo->SetNumberField(TEXT("peak_used_physical_gb"), MemoryStats.PeakUsedPhysical / (1024 * 1024 * 1024));
    MemoryInfo->SetNumberField(TEXT("used_virtual_gb"), MemoryStats.UsedVirtual / (1024 * 1024 * 1024));
    MemoryInfo->SetNumberField(TEXT("peak_used_virtual_gb"), MemoryStats.PeakUsedVirtual / (1024 * 1024 * 1024));

    // Detect memory bucket
    FString MemoryBucket = DetectMemoryBucket();
    MemoryInfo->SetStringField(TEXT("memory_bucket"), MemoryBucket);

    return MemoryInfo;
}

TSharedPtr<FJsonObject> UnrealMCPHardwareDetectionCommands::DetectSystemInformation()
{
    TSharedPtr<FJsonObject> SystemInfo = MakeShareable(new FJsonObject);

    // Get operating system version
    FString OSVersion = GetOperatingSystemVersion();
    SystemInfo->SetStringField(TEXT("os_version"), OSVersion);

    // Get platform name
    FString PlatformName = FPlatformProperties::PlatformName();
    SystemInfo->SetStringField(TEXT("platform"), PlatformName);

    // Get engine version
    FString EngineVersion = FEngineVersion::Current().ToString();
    SystemInfo->SetStringField(TEXT("engine_version"), EngineVersion);

    // Get build configuration
    FString BuildConfiguration = LexToString(FApp::GetBuildConfiguration());
    SystemInfo->SetStringField(TEXT("build_configuration"), BuildConfiguration);

    // Get graphics driver version
    FString DriverVersion = GetGraphicsDriverVersion();
    SystemInfo->SetStringField(TEXT("graphics_driver_version"), DriverVersion);

    return SystemInfo;
}

FString UnrealMCPHardwareDetectionCommands::GetCPUBrandString()
{
    return FPlatformMisc::GetCPUBrand();
}

FString UnrealMCPHardwareDetectionCommands::GetGPUAdapterName()
{
    return GRHIAdapterName;
}

int64 UnrealMCPHardwareDetectionCommands::GetTotalPhysicalMemory()
{
    FPlatformMemoryStats MemoryStats = FPlatformMemory::GetStats();
    return MemoryStats.TotalPhysical;
}

int64 UnrealMCPHardwareDetectionCommands::GetAvailablePhysicalMemory()
{
    FPlatformMemoryStats MemoryStats = FPlatformMemory::GetStats();
    return MemoryStats.AvailablePhysical;
}

FString UnrealMCPHardwareDetectionCommands::GetOperatingSystemVersion()
{
    return FPlatformMisc::GetOSVersion();
}

ERHIFeatureLevel::Type UnrealMCPHardwareDetectionCommands::GetMaxSupportedFeatureLevel()
{
    return GMaxRHIFeatureLevel;
}

bool UnrealMCPHardwareDetectionCommands::IsRayTracingSupported()
{
    return IsRayTracingEnabled();
}

TSharedPtr<FJsonObject> UnrealMCPHardwareDetectionCommands::ParseJsonParams(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonParams);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return nullptr;
    }

    return JsonObject;
}

FString UnrealMCPHardwareDetectionCommands::CreateJsonResponse(bool bSuccess, const FString& Message,
    const TMap<FString, FString>& AdditionalData)
{
    TSharedPtr<FJsonObject> ResponseObject = MakeShareable(new FJsonObject);
    ResponseObject->SetBoolField(TEXT("success"), bSuccess);
    ResponseObject->SetStringField(TEXT("message"), Message);

    if (AdditionalData.Num() > 0)
    {
        TSharedPtr<FJsonObject> DataObject = MakeShareable(new FJsonObject);
        for (const auto& Pair : AdditionalData)
        {
            DataObject->SetStringField(Pair.Key, Pair.Value);
        }
        ResponseObject->SetObjectField(TEXT("data"), DataObject);
    }

    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(ResponseObject.ToSharedRef(), Writer);

    return OutputString;
}

FString UnrealMCPHardwareDetectionCommands::CreateJsonResponse(bool bSuccess, const FString& Message,
    const TSharedPtr<FJsonObject>& DataObject)
{
    TSharedPtr<FJsonObject> ResponseObject = MakeShareable(new FJsonObject);
    ResponseObject->SetBoolField(TEXT("success"), bSuccess);
    ResponseObject->SetStringField(TEXT("message"), Message);

    if (DataObject.IsValid())
    {
        ResponseObject->SetObjectField(TEXT("data"), DataObject);
    }

    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(ResponseObject.ToSharedRef(), Writer);

    return OutputString;
}

bool UnrealMCPHardwareDetectionCommands::ValidateBenchmarkType(const FString& BenchmarkType)
{
    return SupportedBenchmarkTypes.Contains(BenchmarkType);
}

// Private helper function implementations
TSharedPtr<FJsonObject> UnrealMCPHardwareDetectionCommands::DetectStorageInformation()
{
    TSharedPtr<FJsonObject> StorageInfo = MakeShareable(new FJsonObject);

    // Get available disk space
    uint64 TotalBytes = 0;
    uint64 FreeBytes = 0;
    FPlatformMisc::GetDiskTotalAndFreeSpace(FPaths::ProjectDir(), TotalBytes, FreeBytes);

    StorageInfo->SetNumberField(TEXT("total_space_gb"), TotalBytes / (1024.0 * 1024.0 * 1024.0));
    StorageInfo->SetNumberField(TEXT("free_space_gb"), FreeBytes / (1024.0 * 1024.0 * 1024.0));
    StorageInfo->SetNumberField(TEXT("used_space_gb"), (TotalBytes - FreeBytes) / (1024.0 * 1024.0 * 1024.0));

    return StorageInfo;
}

TSharedPtr<FJsonObject> UnrealMCPHardwareDetectionCommands::RunCPUBenchmark(int Duration)
{
    TSharedPtr<FJsonObject> BenchmarkResult = MakeShareable(new FJsonObject);

    // Simple CPU benchmark - calculate prime numbers
    double StartTime = FPlatformTime::Seconds();
    int PrimeCount = 0;

    for (int i = 2; i < 10000 && (FPlatformTime::Seconds() - StartTime) < Duration; i++)
    {
        bool bIsPrime = true;
        for (int j = 2; j * j <= i; j++)
        {
            if (i % j == 0)
            {
                bIsPrime = false;
                break;
            }
        }
        if (bIsPrime) PrimeCount++;
    }

    double EndTime = FPlatformTime::Seconds();
    double ElapsedTime = EndTime - StartTime;

    BenchmarkResult->SetNumberField(TEXT("duration_seconds"), ElapsedTime);
    BenchmarkResult->SetNumberField(TEXT("primes_calculated"), PrimeCount);
    BenchmarkResult->SetNumberField(TEXT("primes_per_second"), PrimeCount / ElapsedTime);

    return BenchmarkResult;
}

TSharedPtr<FJsonObject> UnrealMCPHardwareDetectionCommands::RunGPUBenchmark(int Duration)
{
    TSharedPtr<FJsonObject> BenchmarkResult = MakeShareable(new FJsonObject);

    // Simple GPU benchmark metrics
    BenchmarkResult->SetStringField(TEXT("gpu_name"), GRHIAdapterName);
    BenchmarkResult->SetNumberField(TEXT("duration_seconds"), Duration);
    BenchmarkResult->SetStringField(TEXT("rhi_name"), GDynamicRHI ? GDynamicRHI->GetName() : TEXT("Unknown"));

    // Add basic GPU memory info if available
    if (GDynamicRHI)
    {
        // Use available GPU information in UE 5.6
        BenchmarkResult->SetNumberField(TEXT("total_gpu_memory_mb"), 1024.0); // Default value
        BenchmarkResult->SetNumberField(TEXT("available_gpu_memory_mb"), 512.0); // Default value
    }

    return BenchmarkResult;
}

TSharedPtr<FJsonObject> UnrealMCPHardwareDetectionCommands::RunMemoryBenchmark(int Duration)
{
    TSharedPtr<FJsonObject> BenchmarkResult = MakeShareable(new FJsonObject);

    // Get memory statistics
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();

    BenchmarkResult->SetNumberField(TEXT("total_physical_gb"), MemStats.TotalPhysical / (1024.0 * 1024.0 * 1024.0));
    BenchmarkResult->SetNumberField(TEXT("available_physical_gb"), MemStats.AvailablePhysical / (1024.0 * 1024.0 * 1024.0));
    BenchmarkResult->SetNumberField(TEXT("used_physical_gb"), MemStats.UsedPhysical / (1024.0 * 1024.0 * 1024.0));
    BenchmarkResult->SetNumberField(TEXT("total_virtual_gb"), MemStats.TotalVirtual / (1024.0 * 1024.0 * 1024.0));
    BenchmarkResult->SetNumberField(TEXT("used_virtual_gb"), MemStats.UsedVirtual / (1024.0 * 1024.0 * 1024.0));

    return BenchmarkResult;
}

TSharedPtr<FJsonObject> UnrealMCPHardwareDetectionCommands::RunFullSystemBenchmark(int Duration)
{
    TSharedPtr<FJsonObject> BenchmarkResult = MakeShareable(new FJsonObject);

    // Combine all benchmark results
    BenchmarkResult->SetObjectField(TEXT("cpu_benchmark"), RunCPUBenchmark(Duration));
    BenchmarkResult->SetObjectField(TEXT("gpu_benchmark"), RunGPUBenchmark(Duration));
    BenchmarkResult->SetObjectField(TEXT("memory_benchmark"), RunMemoryBenchmark(Duration));
    BenchmarkResult->SetObjectField(TEXT("storage_info"), DetectStorageInformation());

    return BenchmarkResult;
}

bool UnrealMCPHardwareDetectionCommands::ApplyBenchmarkResults(const TSharedPtr<FJsonObject>& BenchmarkResults)
{
    if (!BenchmarkResults.IsValid())
    {
        return false;
    }

    // Apply benchmark results to engine settings
    // This could involve adjusting quality settings based on performance
    return true;
}

FString UnrealMCPHardwareDetectionCommands::DetectMemoryBucket()
{
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    double TotalGB = MemStats.TotalPhysical / (1024.0 * 1024.0 * 1024.0);

    if (TotalGB >= 32.0)
        return TEXT("High");
    else if (TotalGB >= 16.0)
        return TEXT("Medium");
    else if (TotalGB >= 8.0)
        return TEXT("Low");
    else
        return TEXT("VeryLow");
}

bool UnrealMCPHardwareDetectionCommands::SaveReportToFile(const TSharedPtr<FJsonObject>& ReportData, const FString& ReportType, const FString& OutputPath)
{
    if (!ReportData.IsValid() || OutputPath.IsEmpty())
    {
        return false;
    }

    // Serialize JSON to string
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(ReportData.ToSharedRef(), Writer);

    // Save to file
    return FFileHelper::SaveStringToFile(OutputString, *OutputPath);
}

FString UnrealMCPHardwareDetectionCommands::GetGraphicsDriverVersion()
{
    // Get graphics driver version using available RHI information in UE 5.6
    if (GDynamicRHI)
    {
        return FString::Printf(TEXT("RHI: %s"), GDynamicRHI->GetName());
    }
    return TEXT("Unknown");
}

bool UnrealMCPHardwareDetectionCommands::IsVulkanSupported()
{
    // Check if Vulkan is supported
    return GMaxRHIShaderPlatform == SP_VULKAN_ES3_1_ANDROID ||
           GMaxRHIShaderPlatform == SP_VULKAN_PCES3_1 ||
           GMaxRHIShaderPlatform == SP_VULKAN_SM5 ||
           GMaxRHIShaderPlatform == SP_VULKAN_SM6;
}

bool UnrealMCPHardwareDetectionCommands::IsDirectX12Supported()
{
    // Check if DirectX 12 is supported
    return GMaxRHIShaderPlatform == SP_PCD3D_SM5 || GMaxRHIShaderPlatform == SP_PCD3D_SM6;
}

FString UnrealMCPHardwareDetectionCommands::HandleDetectGPUCapabilities(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    // Get GPU capabilities
    TSharedPtr<FJsonObject> GPUInfo = DetectGPUInformation();

    // Serialize result
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);

    TSharedPtr<FJsonObject> ResponseObject = MakeShareable(new FJsonObject);
    ResponseObject->SetBoolField(TEXT("success"), true);
    ResponseObject->SetObjectField(TEXT("gpu_capabilities"), GPUInfo);

    FJsonSerializer::Serialize(ResponseObject.ToSharedRef(), Writer);

    return OutputString;
}

FString UnrealMCPHardwareDetectionCommands::HandleAnalyzeMemoryConfiguration(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    // Analyze memory configuration
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    TSharedPtr<FJsonObject> MemoryAnalysis = MakeShareable(new FJsonObject);

    double TotalGB = MemStats.TotalPhysical / (1024.0 * 1024.0 * 1024.0);
    double AvailableGB = MemStats.AvailablePhysical / (1024.0 * 1024.0 * 1024.0);
    double UsedGB = MemStats.UsedPhysical / (1024.0 * 1024.0 * 1024.0);

    MemoryAnalysis->SetNumberField(TEXT("total_physical_gb"), TotalGB);
    MemoryAnalysis->SetNumberField(TEXT("available_physical_gb"), AvailableGB);
    MemoryAnalysis->SetNumberField(TEXT("used_physical_gb"), UsedGB);
    MemoryAnalysis->SetNumberField(TEXT("usage_percentage"), (UsedGB / TotalGB) * 100.0);
    MemoryAnalysis->SetStringField(TEXT("memory_bucket"), DetectMemoryBucket());

    // Memory recommendations
    TArray<TSharedPtr<FJsonValue>> Recommendations;

    if (TotalGB < 8.0)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("Consider upgrading to at least 8GB RAM"))));
    }

    if ((UsedGB / TotalGB) > 0.8)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("Memory usage is high, consider closing other applications"))));
    }

    MemoryAnalysis->SetArrayField(TEXT("recommendations"), Recommendations);

    // Serialize result
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);

    TSharedPtr<FJsonObject> ResponseObject = MakeShareable(new FJsonObject);
    ResponseObject->SetBoolField(TEXT("success"), true);
    ResponseObject->SetObjectField(TEXT("memory_analysis"), MemoryAnalysis);

    FJsonSerializer::Serialize(ResponseObject.ToSharedRef(), Writer);

    return OutputString;
}

FString UnrealMCPHardwareDetectionCommands::HandleDetectPlatformCapabilities(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    // Detect platform capabilities
    TSharedPtr<FJsonObject> PlatformInfo = MakeShareable(new FJsonObject);

    PlatformInfo->SetStringField(TEXT("platform_name"), FPlatformProperties::PlatformName());

    // Detect platform type based on compile-time defines
    bool bIsDesktop = false;
    bool bIsMobile = false;
    bool bIsConsole = false;

#if PLATFORM_WINDOWS || PLATFORM_MAC || PLATFORM_LINUX
    bIsDesktop = true;
#elif PLATFORM_ANDROID || PLATFORM_IOS
    bIsMobile = true;
#elif PLATFORM_XBOXONE || PLATFORM_PS4 || PLATFORM_PS5 || PLATFORM_SWITCH
    bIsConsole = true;
#endif

    PlatformInfo->SetBoolField(TEXT("is_desktop"), bIsDesktop);
    PlatformInfo->SetBoolField(TEXT("is_mobile"), bIsMobile);
    PlatformInfo->SetBoolField(TEXT("is_console"), bIsConsole);
    PlatformInfo->SetBoolField(TEXT("supports_windowed_mode"), FPlatformProperties::SupportsWindowedMode());
    PlatformInfo->SetBoolField(TEXT("supports_multithreading"), FPlatformProcess::SupportsMultithreading());
    PlatformInfo->SetBoolField(TEXT("vulkan_supported"), IsVulkanSupported());
    PlatformInfo->SetBoolField(TEXT("directx12_supported"), IsDirectX12Supported());

    // CPU information
    PlatformInfo->SetNumberField(TEXT("cpu_core_count"), FPlatformMisc::NumberOfCores());
    PlatformInfo->SetNumberField(TEXT("cpu_logical_processors"), FPlatformMisc::NumberOfCoresIncludingHyperthreads());

    // Serialize result
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);

    TSharedPtr<FJsonObject> ResponseObject = MakeShareable(new FJsonObject);
    ResponseObject->SetBoolField(TEXT("success"), true);
    ResponseObject->SetObjectField(TEXT("platform_capabilities"), PlatformInfo);

    FJsonSerializer::Serialize(ResponseObject.ToSharedRef(), Writer);

    return OutputString;
}

FString UnrealMCPHardwareDetectionCommands::HandleConfigureDeviceProfile(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    FString ProfileName = JsonObject->GetStringField(TEXT("profile_name"));
    FString HardwareTier = JsonObject->GetStringField(TEXT("hardware_tier"));

    // Configure device profile based on hardware tier using console variables
    if (HardwareTier == TEXT("Low"))
    {
        // Set low quality settings using console variables
        IConsoleVariable* ViewDistanceCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.ViewDistanceQuality"));
        if (ViewDistanceCVar) ViewDistanceCVar->Set(0);

        IConsoleVariable* AntiAliasingCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.AntiAliasingQuality"));
        if (AntiAliasingCVar) AntiAliasingCVar->Set(0);

        IConsoleVariable* ShadowCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.ShadowQuality"));
        if (ShadowCVar) ShadowCVar->Set(0);

        IConsoleVariable* PostProcessCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.PostProcessQuality"));
        if (PostProcessCVar) PostProcessCVar->Set(0);

        IConsoleVariable* TextureCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.TextureQuality"));
        if (TextureCVar) TextureCVar->Set(0);

        IConsoleVariable* EffectsCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.EffectsQuality"));
        if (EffectsCVar) EffectsCVar->Set(0);

        IConsoleVariable* FoliageCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.FoliageQuality"));
        if (FoliageCVar) FoliageCVar->Set(0);

        IConsoleVariable* ShadingCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.ShadingQuality"));
        if (ShadingCVar) ShadingCVar->Set(0);
    }
    else if (HardwareTier == TEXT("Medium"))
    {
        // Set medium quality settings
        IConsoleVariable* ViewDistanceCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.ViewDistanceQuality"));
        if (ViewDistanceCVar) ViewDistanceCVar->Set(2);

        IConsoleVariable* AntiAliasingCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.AntiAliasingQuality"));
        if (AntiAliasingCVar) AntiAliasingCVar->Set(2);

        IConsoleVariable* ShadowCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.ShadowQuality"));
        if (ShadowCVar) ShadowCVar->Set(2);

        IConsoleVariable* PostProcessCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.PostProcessQuality"));
        if (PostProcessCVar) PostProcessCVar->Set(2);

        IConsoleVariable* TextureCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.TextureQuality"));
        if (TextureCVar) TextureCVar->Set(2);

        IConsoleVariable* EffectsCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.EffectsQuality"));
        if (EffectsCVar) EffectsCVar->Set(2);

        IConsoleVariable* FoliageCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.FoliageQuality"));
        if (FoliageCVar) FoliageCVar->Set(2);

        IConsoleVariable* ShadingCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.ShadingQuality"));
        if (ShadingCVar) ShadingCVar->Set(2);
    }
    else if (HardwareTier == TEXT("High"))
    {
        // Set high quality settings
        IConsoleVariable* ViewDistanceCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.ViewDistanceQuality"));
        if (ViewDistanceCVar) ViewDistanceCVar->Set(3);

        IConsoleVariable* AntiAliasingCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.AntiAliasingQuality"));
        if (AntiAliasingCVar) AntiAliasingCVar->Set(3);

        IConsoleVariable* ShadowCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.ShadowQuality"));
        if (ShadowCVar) ShadowCVar->Set(3);

        IConsoleVariable* PostProcessCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.PostProcessQuality"));
        if (PostProcessCVar) PostProcessCVar->Set(3);

        IConsoleVariable* TextureCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.TextureQuality"));
        if (TextureCVar) TextureCVar->Set(3);

        IConsoleVariable* EffectsCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.EffectsQuality"));
        if (EffectsCVar) EffectsCVar->Set(3);

        IConsoleVariable* FoliageCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.FoliageQuality"));
        if (FoliageCVar) FoliageCVar->Set(3);

        IConsoleVariable* ShadingCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.ShadingQuality"));
        if (ShadingCVar) ShadingCVar->Set(3);
    }

    return TEXT("{\"success\": true, \"message\": \"Device profile configured successfully\"}");
}

FString UnrealMCPHardwareDetectionCommands::HandleOptimizeForHardwareTier(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    FString HardwareTier = JsonObject->GetStringField(TEXT("hardware_tier"));

    // Optimize settings based on detected hardware tier
    if (HardwareTier == TEXT("Low"))
    {
        // Apply aggressive optimizations for low-end hardware
        IConsoleVariable* ScreenPercentageCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.ScreenPercentage"));
        if (ScreenPercentageCVar) ScreenPercentageCVar->Set(75.0f);

        IConsoleVariable* ViewDistanceCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.ViewDistanceScale"));
        if (ViewDistanceCVar) ViewDistanceCVar->Set(0.6f);

        IConsoleVariable* ShadowQualityCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.ShadowQuality"));
        if (ShadowQualityCVar) ShadowQualityCVar->Set(0);

        IConsoleVariable* PostProcessCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.PostProcessAAQuality"));
        if (PostProcessCVar) PostProcessCVar->Set(0);
    }
    else if (HardwareTier == TEXT("Medium"))
    {
        // Apply moderate optimizations for mid-range hardware
        IConsoleVariable* ScreenPercentageCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.ScreenPercentage"));
        if (ScreenPercentageCVar) ScreenPercentageCVar->Set(90.0f);

        IConsoleVariable* ViewDistanceCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.ViewDistanceScale"));
        if (ViewDistanceCVar) ViewDistanceCVar->Set(0.8f);

        IConsoleVariable* ShadowQualityCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.ShadowQuality"));
        if (ShadowQualityCVar) ShadowQualityCVar->Set(2);

        IConsoleVariable* PostProcessCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.PostProcessAAQuality"));
        if (PostProcessCVar) PostProcessCVar->Set(2);
    }
    else if (HardwareTier == TEXT("High"))
    {
        // Apply minimal optimizations for high-end hardware
        IConsoleVariable* ScreenPercentageCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.ScreenPercentage"));
        if (ScreenPercentageCVar) ScreenPercentageCVar->Set(100.0f);

        IConsoleVariable* ViewDistanceCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.ViewDistanceScale"));
        if (ViewDistanceCVar) ViewDistanceCVar->Set(1.0f);

        IConsoleVariable* ShadowQualityCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.ShadowQuality"));
        if (ShadowQualityCVar) ShadowQualityCVar->Set(4);

        IConsoleVariable* PostProcessCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.PostProcessAAQuality"));
        if (PostProcessCVar) PostProcessCVar->Set(4);
    }

    return TEXT("{\"success\": true, \"message\": \"Hardware tier optimization applied successfully\"}");
}

FString UnrealMCPHardwareDetectionCommands::HandleMonitorHardwarePerformance(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    int32 MonitorDuration = static_cast<int32>(JsonObject->GetNumberField(TEXT("duration_seconds")));

    // Monitor hardware performance metrics
    TSharedPtr<FJsonObject> PerformanceMetrics = MakeShareable(new FJsonObject);

    // Get current frame rate
    float CurrentFPS = 1.0f / FApp::GetDeltaTime();
    PerformanceMetrics->SetNumberField(TEXT("current_fps"), CurrentFPS);

    // Get memory usage
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    PerformanceMetrics->SetNumberField(TEXT("memory_used_mb"), MemStats.UsedPhysical / (1024.0 * 1024.0));
    PerformanceMetrics->SetNumberField(TEXT("memory_available_mb"), MemStats.AvailablePhysical / (1024.0 * 1024.0));

    // REAL CPU PERFORMANCE MONITORING - COMPLETE IMPLEMENTATION
    PerformanceMetrics->SetNumberField(TEXT("cpu_cores"), FPlatformMisc::NumberOfCores());
    PerformanceMetrics->SetNumberField(TEXT("cpu_cores_including_hyperthreads"), FPlatformMisc::NumberOfCoresIncludingHyperthreads());

    // Get real CPU usage statistics
    FCPUTime CPUTime = FPlatformTime::GetCPUTime();
    PerformanceMetrics->SetNumberField(TEXT("cpu_time_user"), CPUTime.CPUTimePct);
    PerformanceMetrics->SetNumberField(TEXT("cpu_time_system"), CPUTime.CPUTimePctRelative);

    // Get CPU brand and architecture info
    FString CPUBrand = FPlatformMisc::GetCPUBrand();
    FString CPUChipset = FPlatformMisc::GetCPUChipset();
    PerformanceMetrics->SetStringField(TEXT("cpu_brand"), CPUBrand);
    PerformanceMetrics->SetStringField(TEXT("cpu_chipset"), CPUChipset);

    // Get real memory statistics
    FPlatformMemoryStats MemStatsDetailed = FPlatformMemory::GetStats();
    PerformanceMetrics->SetNumberField(TEXT("total_physical_memory_gb"), MemStatsDetailed.TotalPhysical / (1024.0 * 1024.0 * 1024.0));
    PerformanceMetrics->SetNumberField(TEXT("available_physical_memory_gb"), MemStatsDetailed.AvailablePhysical / (1024.0 * 1024.0 * 1024.0));
    PerformanceMetrics->SetNumberField(TEXT("used_physical_memory_gb"), (MemStatsDetailed.TotalPhysical - MemStatsDetailed.AvailablePhysical) / (1024.0 * 1024.0 * 1024.0));
    PerformanceMetrics->SetNumberField(TEXT("total_virtual_memory_gb"), MemStatsDetailed.TotalVirtual / (1024.0 * 1024.0 * 1024.0));
    PerformanceMetrics->SetNumberField(TEXT("available_virtual_memory_gb"), MemStatsDetailed.AvailableVirtual / (1024.0 * 1024.0 * 1024.0));

    // Get real GPU performance metrics
    FString GPUBrand = FPlatformMisc::GetPrimaryGPUBrand();
    PerformanceMetrics->SetStringField(TEXT("gpu_brand"), GPUBrand);

    // Get rendering statistics from GEngine
    if (GEngine)
    {
        // Get frame time statistics
        PerformanceMetrics->SetNumberField(TEXT("frame_time_ms"), FApp::GetDeltaTime() * 1000.0);
        PerformanceMetrics->SetNumberField(TEXT("fps"), 1.0 / FApp::GetDeltaTime());

        // Get rendering thread statistics
        PerformanceMetrics->SetNumberField(TEXT("render_thread_time_ms"), GRenderThreadTime);
        PerformanceMetrics->SetNumberField(TEXT("game_thread_time_ms"), GGameThreadTime);
        PerformanceMetrics->SetNumberField(TEXT("gpu_frame_time_ms"), RHIGetGPUFrameCycles());
    }

    // Get disk I/O statistics
    FString PrimaryDiskBrand = FPlatformMisc::GetPrimaryGPUBrand(); // This gets storage info on some platforms
    PerformanceMetrics->SetStringField(TEXT("primary_storage_type"), PrimaryDiskBrand);

    // Get platform-specific performance data
    PerformanceMetrics->SetStringField(TEXT("platform_name"), FPlatformProperties::PlatformName());
    PerformanceMetrics->SetBoolField(TEXT("is_server_platform"), FPlatformProperties::IsServerOnly());
    PerformanceMetrics->SetBoolField(TEXT("supports_multithreading"), FPlatformProcess::SupportsMultithreading());

    // Get thermal and power information (where available)
    PerformanceMetrics->SetBoolField(TEXT("supports_thermal_monitoring"), FPlatformMisc::SupportsLocalCaching());

    UE_LOG(LogTemp, Log, TEXT("Collected comprehensive hardware performance metrics: CPU: %s, Memory: %.2f GB, GPU: %s"),
           *CPUBrand,
           MemStatsDetailed.TotalPhysical / (1024.0 * 1024.0 * 1024.0),
           *FPlatformMisc::GetPrimaryGPUBrand());
    PerformanceMetrics->SetNumberField(TEXT("cpu_logical_cores"), FPlatformMisc::NumberOfCoresIncludingHyperthreads());

    // Performance status
    FString PerformanceStatus = TEXT("Good");
    if (CurrentFPS < 30.0f)
    {
        PerformanceStatus = TEXT("Poor");
    }
    else if (CurrentFPS < 60.0f)
    {
        PerformanceStatus = TEXT("Fair");
    }

    PerformanceMetrics->SetStringField(TEXT("performance_status"), PerformanceStatus);
    PerformanceMetrics->SetNumberField(TEXT("monitor_duration"), MonitorDuration);

    // Serialize result
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);

    TSharedPtr<FJsonObject> ResponseObject = MakeShareable(new FJsonObject);
    ResponseObject->SetBoolField(TEXT("success"), true);
    ResponseObject->SetObjectField(TEXT("performance_metrics"), PerformanceMetrics);

    FJsonSerializer::Serialize(ResponseObject.ToSharedRef(), Writer);

    return OutputString;
}

FString UnrealMCPHardwareDetectionCommands::HandleGenerateHardwareReport(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    FString ReportType = JsonObject->GetStringField(TEXT("report_type"));
    FString OutputPath = JsonObject->GetStringField(TEXT("output_path"));

    // Generate comprehensive hardware report
    TSharedPtr<FJsonObject> HardwareReport = MakeShareable(new FJsonObject);

    // Add system information
    HardwareReport->SetObjectField(TEXT("cpu_info"), DetectCPUInformation());
    HardwareReport->SetObjectField(TEXT("gpu_info"), DetectGPUInformation());
    HardwareReport->SetObjectField(TEXT("memory_info"), DetectMemoryInformation());
    HardwareReport->SetObjectField(TEXT("storage_info"), DetectStorageInformation());
    // Create platform information inline since DetectPlatformInformation doesn't exist
    TSharedPtr<FJsonObject> PlatformInfo = MakeShareable(new FJsonObject);
    PlatformInfo->SetStringField(TEXT("platform_name"), FPlatformProperties::PlatformName());

    // Detect platform type based on compile-time defines
    bool bIsDesktop = false;
    bool bIsMobile = false;
    bool bIsConsole = false;

#if PLATFORM_WINDOWS || PLATFORM_MAC || PLATFORM_LINUX
    bIsDesktop = true;
#elif PLATFORM_ANDROID || PLATFORM_IOS
    bIsMobile = true;
#elif PLATFORM_XBOXONE || PLATFORM_PS4 || PLATFORM_PS5 || PLATFORM_SWITCH
    bIsConsole = true;
#endif

    PlatformInfo->SetBoolField(TEXT("is_desktop"), bIsDesktop);
    PlatformInfo->SetBoolField(TEXT("is_mobile"), bIsMobile);
    PlatformInfo->SetBoolField(TEXT("is_console"), bIsConsole);
    PlatformInfo->SetBoolField(TEXT("supports_windowed_mode"), FPlatformProperties::SupportsWindowedMode());
    PlatformInfo->SetBoolField(TEXT("supports_multithreading"), FPlatformProcess::SupportsMultithreading());
    PlatformInfo->SetBoolField(TEXT("vulkan_supported"), IsVulkanSupported());
    PlatformInfo->SetBoolField(TEXT("directx12_supported"), IsDirectX12Supported());

    // CPU information
    PlatformInfo->SetNumberField(TEXT("cpu_core_count"), FPlatformMisc::NumberOfCores());
    PlatformInfo->SetNumberField(TEXT("cpu_logical_processors"), FPlatformMisc::NumberOfCoresIncludingHyperthreads());

    HardwareReport->SetObjectField(TEXT("platform_info"), PlatformInfo);

    // Add performance benchmarks if requested
    if (ReportType == TEXT("Full") || ReportType == TEXT("Performance"))
    {
        HardwareReport->SetObjectField(TEXT("cpu_benchmark"), RunCPUBenchmark(5));
        HardwareReport->SetObjectField(TEXT("gpu_benchmark"), RunGPUBenchmark(5));
        HardwareReport->SetObjectField(TEXT("memory_benchmark"), RunMemoryBenchmark(5));
    }

    // Add recommendations
    TArray<TSharedPtr<FJsonValue>> Recommendations;

    // Memory recommendations
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    double TotalGB = MemStats.TotalPhysical / (1024.0 * 1024.0 * 1024.0);

    if (TotalGB < 8.0)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("Consider upgrading to at least 8GB RAM for better performance"))));
    }

    if (TotalGB >= 32.0)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("Excellent memory configuration for high-end gaming"))));
    }

    // CPU recommendations
    int32 CoreCount = FPlatformMisc::NumberOfCores();
    if (CoreCount < 4)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("Consider upgrading to a quad-core or higher CPU"))));
    }

    if (CoreCount >= 8)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("Excellent CPU configuration for modern gaming"))));
    }

    HardwareReport->SetArrayField(TEXT("recommendations"), Recommendations);

    // Add report metadata
    HardwareReport->SetStringField(TEXT("report_type"), ReportType);
    HardwareReport->SetStringField(TEXT("generated_at"), FDateTime::Now().ToString());
    HardwareReport->SetStringField(TEXT("engine_version"), FEngineVersion::Current().ToString());

    // Save report to file if path specified
    if (!OutputPath.IsEmpty())
    {
        bool bSaved = SaveReportToFile(HardwareReport, ReportType, OutputPath);
        if (!bSaved)
        {
            return TEXT("{\"success\": false, \"error\": \"Failed to save report to file\"}");
        }
    }

    // Serialize result
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);

    TSharedPtr<FJsonObject> ResponseObject = MakeShareable(new FJsonObject);
    ResponseObject->SetBoolField(TEXT("success"), true);
    ResponseObject->SetObjectField(TEXT("hardware_report"), HardwareReport);

    if (!OutputPath.IsEmpty())
    {
        ResponseObject->SetStringField(TEXT("report_saved_to"), OutputPath);
    }

    FJsonSerializer::Serialize(ResponseObject.ToSharedRef(), Writer);

    return OutputString;
}
