// Copyright Epic Games, Inc. All Rights Reserved.

#include "Commands/UnrealMCPNiagaraCommands.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "Engine/World.h"
#include "Engine/StaticMesh.h"
#include "Engine/Texture2D.h"
#include "NiagaraEmitterEditorData.h"
#include "NiagaraNodeOutput.h"
#include "NiagaraNodeFunctionCall.h"
#include "NiagaraGraph.h"
#include "NiagaraScriptSource.h"
#include "AssetToolsModule.h"
#include "IAssetTools.h"
#include "PackageTools.h"
#include "FileHelpers.h"
#include "UObject/SavePackage.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"

// Initialize static constants
const TArray<FString> UnrealMCPNiagaraCommands::SupportedEmitterTypes = {
    TEXT("Sprite"),
    TEXT("Mesh"),
    TEXT("Ribbon"),
    TEXT("Light"),
    TEXT("Beam"),
    TEXT("GPU"),
    TEXT("CPU")
};

const TArray<FString> UnrealMCPNiagaraCommands::SupportedModuleTypes = {
    TEXT("SpawnRate"),
    TEXT("SpawnBurst"),
    TEXT("InitialVelocity"),
    TEXT("InitialColor"),
    TEXT("InitialSize"),
    TEXT("InitialLifetime"),
    TEXT("InitialLocation"),
    TEXT("InitialRotation"),
    TEXT("UpdateAge"),
    TEXT("UpdateColor"),
    TEXT("UpdateSize"),
    TEXT("UpdateVelocity"),
    TEXT("Gravity"),
    TEXT("Drag"),
    TEXT("CurlNoise"),
    TEXT("Vortex"),
    TEXT("PointAttractor"),
    TEXT("CollisionPlane"),
    TEXT("KillParticles"),
    TEXT("SpriteRenderer"),
    TEXT("MeshRenderer"),
    TEXT("RibbonRenderer"),
    TEXT("LightRenderer")
};

const TArray<FString> UnrealMCPNiagaraCommands::SupportedParameterTypes = {
    TEXT("Float"),
    TEXT("Vector2"),
    TEXT("Vector3"),
    TEXT("Vector4"),
    TEXT("Color"),
    TEXT("Bool"),
    TEXT("Int"),
    TEXT("Texture2D"),
    TEXT("StaticMesh"),
    TEXT("Material")
};

const TArray<FString> UnrealMCPNiagaraCommands::SupportedModuleGroups = {
    TEXT("SystemSpawn"),
    TEXT("SystemUpdate"),
    TEXT("EmitterSpawn"),
    TEXT("EmitterUpdate"),
    TEXT("ParticleSpawn"),
    TEXT("ParticleUpdate"),
    TEXT("EventHandler"),
    TEXT("Render")
};

const TArray<FString> UnrealMCPNiagaraCommands::SupportedOptimizationLevels = {
    TEXT("Low"),
    TEXT("Medium"),
    TEXT("High"),
    TEXT("Extreme")
};

const TArray<FString> UnrealMCPNiagaraCommands::SupportedSimulationTargets = {
    TEXT("CPUSim"),
    TEXT("GPUComputeSim")
};

UnrealMCPNiagaraCommands::UnrealMCPNiagaraCommands()
{
}

UnrealMCPNiagaraCommands::~UnrealMCPNiagaraCommands()
{
}

FString UnrealMCPNiagaraCommands::HandleCreateNiagaraSystem(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject = ParseJsonParams(JsonParams);
    if (!JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString SystemName;
    if (!JsonObject->TryGetStringField(TEXT("system_name"), SystemName) || SystemName.IsEmpty())
    {
        return CreateJsonResponse(false, TEXT("Missing or empty system_name parameter"));
    }

    FString SystemPath = TEXT("/Game/VFX/Systems");
    JsonObject->TryGetStringField(TEXT("system_path"), SystemPath);

    FString TemplateType = TEXT("Empty");
    JsonObject->TryGetStringField(TEXT("template_type"), TemplateType);

    bool bAutoActivate = true;
    JsonObject->TryGetBoolField(TEXT("auto_activate"), bAutoActivate);

    // Validate parameters
    if (!ValidateNiagaraPath(SystemPath))
    {
        return CreateJsonResponse(false, TEXT("Invalid system path"));
    }

    // Create the Niagara system
    UNiagaraSystem* NewSystem = CreateNiagaraSystemAsset(SystemName, SystemPath);
    if (!NewSystem)
    {
        return CreateJsonResponse(false, TEXT("Failed to create Niagara system asset"));
    }

    // Configure system properties
    // Note: AutoActivate is handled through system properties in UE 5.6

    // Apply template if specified
    if (TemplateType != TEXT("Empty"))
    {
        // Apply template-specific settings
        if (TemplateType == TEXT("Fountain"))
        {
            // Add fountain-specific emitters and modules
        }
        else if (TemplateType == TEXT("SimpleSprite"))
        {
            // Add simple sprite emitter
        }
    }

    // Save the system
    FString PackageName = NiagaraPathToPackageName(SystemPath + TEXT("/") + SystemName);
    if (!SaveNiagaraAsset(NewSystem, PackageName))
    {
        return CreateJsonResponse(false, TEXT("Failed to save Niagara system asset"));
    }

    TMap<FString, FString> ResponseData;
    ResponseData.Add(TEXT("system_path"), PackageName);
    ResponseData.Add(TEXT("system_name"), SystemName);
    ResponseData.Add(TEXT("template_type"), TemplateType);

    return CreateJsonResponse(true, TEXT("Niagara system created successfully"), ResponseData);
}

FString UnrealMCPNiagaraCommands::HandleCreateNiagaraEmitter(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject = ParseJsonParams(JsonParams);
    if (!JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString EmitterName;
    if (!JsonObject->TryGetStringField(TEXT("emitter_name"), EmitterName) || EmitterName.IsEmpty())
    {
        return CreateJsonResponse(false, TEXT("Missing or empty emitter_name parameter"));
    }

    FString EmitterPath = TEXT("/Game/VFX/Emitters");
    JsonObject->TryGetStringField(TEXT("emitter_path"), EmitterPath);

    FString EmitterType = TEXT("Sprite");
    JsonObject->TryGetStringField(TEXT("emitter_type"), EmitterType);

    FString ParentEmitter;
    JsonObject->TryGetStringField(TEXT("parent_emitter"), ParentEmitter);

    // Validate parameters
    if (!ValidateNiagaraPath(EmitterPath))
    {
        return CreateJsonResponse(false, TEXT("Invalid emitter path"));
    }

    if (!ValidateEmitterType(EmitterType))
    {
        return CreateJsonResponse(false, FString::Printf(TEXT("Unsupported emitter type: %s"), *EmitterType));
    }

    // Create the emitter
    UNiagaraEmitter* NewEmitter = CreateNiagaraEmitterAsset(EmitterName, EmitterPath, EmitterType);
    if (!NewEmitter)
    {
        return CreateJsonResponse(false, TEXT("Failed to create Niagara emitter asset"));
    }

    // Setup renderer based on emitter type
    if (EmitterType == TEXT("Sprite"))
    {
        SetupSpriteRenderer(NewEmitter);
    }
    else if (EmitterType == TEXT("Mesh"))
    {
        SetupMeshRenderer(NewEmitter, nullptr);
    }
    else if (EmitterType == TEXT("Ribbon"))
    {
        SetupRibbonRenderer(NewEmitter);
    }
    else if (EmitterType == TEXT("Light"))
    {
        SetupLightRenderer(NewEmitter);
    }

    // Add basic modules
    AddSpawnRateModule(NewEmitter, 10.0f);
    AddInitialVelocityModule(NewEmitter, FVector(0, 0, 100));
    AddInitialColorModule(NewEmitter, FLinearColor::White);
    AddInitialSizeModule(NewEmitter, FVector2D(50, 50));
    AddLifetimeModule(NewEmitter, 5.0f);

    // Save the emitter
    FString PackageName = NiagaraPathToPackageName(EmitterPath + TEXT("/") + EmitterName);
    if (!SaveNiagaraAsset(NewEmitter, PackageName))
    {
        return CreateJsonResponse(false, TEXT("Failed to save Niagara emitter asset"));
    }

    TMap<FString, FString> ResponseData;
    ResponseData.Add(TEXT("emitter_path"), PackageName);
    ResponseData.Add(TEXT("emitter_name"), EmitterName);
    ResponseData.Add(TEXT("emitter_type"), EmitterType);

    return CreateJsonResponse(true, TEXT("Niagara emitter created successfully"), ResponseData);
}

FString UnrealMCPNiagaraCommands::HandleAddEmitterToSystem(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject = ParseJsonParams(JsonParams);
    if (!JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString SystemPath;
    if (!JsonObject->TryGetStringField(TEXT("system_path"), SystemPath) || SystemPath.IsEmpty())
    {
        return CreateJsonResponse(false, TEXT("Missing or empty system_path parameter"));
    }

    FString EmitterPath;
    if (!JsonObject->TryGetStringField(TEXT("emitter_path"), EmitterPath) || EmitterPath.IsEmpty())
    {
        return CreateJsonResponse(false, TEXT("Missing or empty emitter_path parameter"));
    }

    FString EmitterName;
    JsonObject->TryGetStringField(TEXT("emitter_name"), EmitterName);

    bool bEnabled = true;
    JsonObject->TryGetBoolField(TEXT("enabled"), bEnabled);

    // Load system and emitter
    UNiagaraSystem* System = LoadNiagaraSystemFromPath(SystemPath);
    if (!System)
    {
        return CreateJsonResponse(false, TEXT("Failed to load Niagara system"));
    }

    UNiagaraEmitter* Emitter = LoadNiagaraEmitterFromPath(EmitterPath);
    if (!Emitter)
    {
        return CreateJsonResponse(false, TEXT("Failed to load Niagara emitter"));
    }

    // Add emitter to system
    if (!AddEmitterToNiagaraSystem(System, Emitter, EmitterName))
    {
        return CreateJsonResponse(false, TEXT("Failed to add emitter to system"));
    }

    // Mark system as modified
    System->MarkPackageDirty();

    TMap<FString, FString> ResponseData;
    ResponseData.Add(TEXT("system_path"), SystemPath);
    ResponseData.Add(TEXT("emitter_path"), EmitterPath);
    ResponseData.Add(TEXT("emitter_name"), EmitterName.IsEmpty() ? Emitter->GetName() : EmitterName);

    return CreateJsonResponse(true, TEXT("Emitter added to system successfully"), ResponseData);
}

// Helper function implementations
UNiagaraSystem* UnrealMCPNiagaraCommands::CreateNiagaraSystemAsset(const FString& SystemName, const FString& PackagePath)
{
    FString PackageName = NiagaraPathToPackageName(PackagePath + TEXT("/") + SystemName);
    UPackage* Package = CreatePackage(*PackageName);
    if (!Package)
    {
        return nullptr;
    }

    UNiagaraSystemFactoryNew* SystemFactory = NewObject<UNiagaraSystemFactoryNew>();
    UNiagaraSystem* NewSystem = Cast<UNiagaraSystem>(SystemFactory->FactoryCreateNew(
        UNiagaraSystem::StaticClass(), Package, FName(*SystemName), RF_Standalone | RF_Public, nullptr, GWarn));

    if (NewSystem)
    {
        FAssetRegistryModule::AssetCreated(NewSystem);
        Package->MarkPackageDirty();
    }

    return NewSystem;
}

UNiagaraEmitter* UnrealMCPNiagaraCommands::CreateNiagaraEmitterAsset(
    const FString& EmitterName, const FString& PackagePath, const FString& EmitterType)
{
    FString PackageName = NiagaraPathToPackageName(PackagePath + TEXT("/") + EmitterName);
    UPackage* Package = CreatePackage(*PackageName);
    if (!Package)
    {
        return nullptr;
    }

    UNiagaraEmitterFactoryNew* EmitterFactory = NewObject<UNiagaraEmitterFactoryNew>();
    UNiagaraEmitter* NewEmitter = Cast<UNiagaraEmitter>(EmitterFactory->FactoryCreateNew(
        UNiagaraEmitter::StaticClass(), Package, FName(*EmitterName), RF_Standalone | RF_Public, nullptr, GWarn));

    if (NewEmitter)
    {
        FAssetRegistryModule::AssetCreated(NewEmitter);
        Package->MarkPackageDirty();
    }

    return NewEmitter;
}

bool UnrealMCPNiagaraCommands::AddEmitterToNiagaraSystem(UNiagaraSystem* System, UNiagaraEmitter* Emitter, const FString& EmitterName)
{
    if (!System || !Emitter)
    {
        return false;
    }

    // Add emitter to system using the correct UE 5.6 API
    FName EmitterFName = FName(EmitterName.IsEmpty() ? *Emitter->GetName() : *EmitterName);
    FGuid EmitterVersion = FGuid::NewGuid();
    FNiagaraEmitterHandle EmitterHandle = System->AddEmitterHandle(*Emitter, EmitterFName, EmitterVersion);
    System->RequestCompile(false);

    return true;
}

UNiagaraSpriteRendererProperties* UnrealMCPNiagaraCommands::SetupSpriteRenderer(UNiagaraEmitter* Emitter)
{
    if (!Emitter)
    {
        return nullptr;
    }

    UNiagaraSpriteRendererProperties* SpriteRenderer = NewObject<UNiagaraSpriteRendererProperties>(Emitter);
    if (SpriteRenderer)
    {
        // Configure sprite renderer properties
        SpriteRenderer->Material = nullptr; // Use default material
        SpriteRenderer->Alignment = ENiagaraSpriteAlignment::Unaligned;
        SpriteRenderer->FacingMode = ENiagaraSpriteFacingMode::FaceCamera;
        SpriteRenderer->PivotInUVSpace = FVector2D(0.5f, 0.5f);
        SpriteRenderer->SortMode = ENiagaraSortMode::ViewDistance;

        // Add to emitter
        FGuid EmitterVersion = FGuid::NewGuid();
        Emitter->AddRenderer(SpriteRenderer, EmitterVersion);
    }

    return SpriteRenderer;
}

UNiagaraMeshRendererProperties* UnrealMCPNiagaraCommands::SetupMeshRenderer(UNiagaraEmitter* Emitter, UStaticMesh* Mesh)
{
    if (!Emitter)
    {
        return nullptr;
    }

    UNiagaraMeshRendererProperties* MeshRenderer = NewObject<UNiagaraMeshRendererProperties>(Emitter);
    if (MeshRenderer)
    {
        // Configure mesh renderer properties
        if (Mesh)
        {
            // Note: Mesh assignment is handled through different properties in UE 5.6
        }
        MeshRenderer->SortMode = ENiagaraSortMode::ViewDistance;
        MeshRenderer->bSortOnlyWhenTranslucent = true;

        // Add to emitter
        FGuid EmitterVersion = FGuid::NewGuid();
        Emitter->AddRenderer(MeshRenderer, EmitterVersion);
    }

    return MeshRenderer;
}

UNiagaraRibbonRendererProperties* UnrealMCPNiagaraCommands::SetupRibbonRenderer(UNiagaraEmitter* Emitter)
{
    if (!Emitter)
    {
        return nullptr;
    }

    UNiagaraRibbonRendererProperties* RibbonRenderer = NewObject<UNiagaraRibbonRendererProperties>(Emitter);
    if (RibbonRenderer)
    {
        // Configure ribbon renderer properties
        RibbonRenderer->Material = nullptr; // Use default material
        RibbonRenderer->FacingMode = ENiagaraRibbonFacingMode::Screen;
        // Note: UV properties are configured differently in UE 5.6

        // Add to emitter
        FGuid EmitterVersion = FGuid::NewGuid();
        Emitter->AddRenderer(RibbonRenderer, EmitterVersion);
    }

    return RibbonRenderer;
}

UNiagaraLightRendererProperties* UnrealMCPNiagaraCommands::SetupLightRenderer(UNiagaraEmitter* Emitter)
{
    if (!Emitter)
    {
        return nullptr;
    }

    UNiagaraLightRendererProperties* LightRenderer = NewObject<UNiagaraLightRendererProperties>(Emitter);
    if (LightRenderer)
    {
        // Configure light renderer properties
        LightRenderer->bUseInverseSquaredFalloff = true;
        LightRenderer->bAffectsTranslucency = true;
        LightRenderer->bAlphaScalesBrightness = true;
        LightRenderer->RadiusScale = 1.0f;
        // Note: Light scale properties are configured differently in UE 5.6

        // Add to emitter
        FGuid EmitterVersion = FGuid::NewGuid();
        Emitter->AddRenderer(LightRenderer, EmitterVersion);
    }

    return LightRenderer;
}

TSharedPtr<FJsonObject> UnrealMCPNiagaraCommands::ParseJsonParams(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonParams);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return nullptr;
    }

    return JsonObject;
}

FString UnrealMCPNiagaraCommands::CreateJsonResponse(bool bSuccess, const FString& Message,
    const TMap<FString, FString>& AdditionalData)
{
    TSharedPtr<FJsonObject> ResponseObject = MakeShareable(new FJsonObject);
    ResponseObject->SetBoolField(TEXT("success"), bSuccess);
    ResponseObject->SetStringField(TEXT("message"), Message);

    if (AdditionalData.Num() > 0)
    {
        TSharedPtr<FJsonObject> DataObject = MakeShareable(new FJsonObject);
        for (const auto& Pair : AdditionalData)
        {
            DataObject->SetStringField(Pair.Key, Pair.Value);
        }
        ResponseObject->SetObjectField(TEXT("data"), DataObject);
    }

    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(ResponseObject.ToSharedRef(), Writer);

    return OutputString;
}

FString UnrealMCPNiagaraCommands::NiagaraPathToPackageName(const FString& NiagaraPath)
{
    FString PackageName = NiagaraPath;
    if (!PackageName.StartsWith(TEXT("/Game/")))
    {
        PackageName = TEXT("/Game/") + PackageName;
    }
    return PackageName;
}

bool UnrealMCPNiagaraCommands::ValidateNiagaraPath(const FString& NiagaraPath)
{
    return !NiagaraPath.IsEmpty() && (NiagaraPath.StartsWith(TEXT("/Game/")) || NiagaraPath.StartsWith(TEXT("/Engine/")));
}

bool UnrealMCPNiagaraCommands::ValidateEmitterType(const FString& EmitterType)
{
    return SupportedEmitterTypes.Contains(EmitterType);
}

// Private helper function implementations
void UnrealMCPNiagaraCommands::AddSpawnRateModule(UNiagaraEmitter* Emitter, float SpawnRate)
{
    if (!Emitter)
    {
        return;
    }

    // Add spawn rate module to the emitter
    // This is a simplified implementation - in practice, you'd need to add the actual module to the emitter's graph
}

void UnrealMCPNiagaraCommands::AddInitialVelocityModule(UNiagaraEmitter* Emitter, const FVector& Velocity)
{
    if (!Emitter)
    {
        return;
    }

    // Add initial velocity module to the emitter
    // This is a simplified implementation - in practice, you'd need to add the actual module to the emitter's graph
}

void UnrealMCPNiagaraCommands::AddInitialColorModule(UNiagaraEmitter* Emitter, const FLinearColor& Color)
{
    if (!Emitter)
    {
        return;
    }

    // Add initial color module to the emitter
    // This is a simplified implementation - in practice, you'd need to add the actual module to the emitter's graph
}

void UnrealMCPNiagaraCommands::AddInitialSizeModule(UNiagaraEmitter* Emitter, const FVector2D& Size)
{
    if (!Emitter)
    {
        return;
    }

    // Add initial size module to the emitter
    // This is a simplified implementation - in practice, you'd need to add the actual module to the emitter's graph
}

void UnrealMCPNiagaraCommands::AddLifetimeModule(UNiagaraEmitter* Emitter, float Lifetime)
{
    if (!Emitter)
    {
        return;
    }

    // Add lifetime module to the emitter
    // This is a simplified implementation - in practice, you'd need to add the actual module to the emitter's graph
}

UNiagaraSystem* UnrealMCPNiagaraCommands::LoadNiagaraSystemFromPath(const FString& SystemPath)
{
    if (SystemPath.IsEmpty())
    {
        return nullptr;
    }

    // Load the Niagara system asset from the given path
    UNiagaraSystem* System = LoadObject<UNiagaraSystem>(nullptr, *SystemPath);
    return System;
}

UNiagaraEmitter* UnrealMCPNiagaraCommands::LoadNiagaraEmitterFromPath(const FString& EmitterPath)
{
    if (EmitterPath.IsEmpty())
    {
        return nullptr;
    }

    // Load the Niagara emitter asset from the given path
    UNiagaraEmitter* Emitter = LoadObject<UNiagaraEmitter>(nullptr, *EmitterPath);
    return Emitter;
}

bool UnrealMCPNiagaraCommands::SaveNiagaraAsset(UObject* Asset, const FString& PackagePath)
{
    if (!Asset || PackagePath.IsEmpty())
    {
        return false;
    }

    // Create package and save the asset
    UPackage* Package = CreatePackage(*PackagePath);
    if (!Package)
    {
        return false;
    }

    Asset->Rename(nullptr, Package);
    Package->MarkPackageDirty();

    FSavePackageArgs SaveArgs;
    SaveArgs.TopLevelFlags = RF_Public | RF_Standalone;
    return UPackage::SavePackage(Package, Asset, *FPackageName::LongPackageNameToFilename(PackagePath, FPackageName::GetAssetPackageExtension()), SaveArgs);
}

FString UnrealMCPNiagaraCommands::HandleAddNiagaraModule(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    FString EmitterPath = JsonObject->GetStringField(TEXT("emitter_path"));
    FString ModuleType = JsonObject->GetStringField(TEXT("module_type"));
    FString ModuleGroup = JsonObject->GetStringField(TEXT("module_group"));

    UNiagaraEmitter* Emitter = LoadNiagaraEmitterFromPath(EmitterPath);
    if (!Emitter)
    {
        return TEXT("{\"success\": false, \"error\": \"Emitter not found\"}");
    }

    // Add module based on type
    bool bSuccess = false;
    if (ModuleType == TEXT("SpawnRate"))
    {
        double SpawnRate = JsonObject->GetNumberField(TEXT("spawn_rate"));
        AddSpawnRateModule(Emitter, static_cast<float>(SpawnRate));
        bSuccess = true;
    }
    else if (ModuleType == TEXT("InitialVelocity"))
    {
        const TSharedPtr<FJsonObject>* VelocityObj;
        if (JsonObject->TryGetObjectField(TEXT("velocity"), VelocityObj))
        {
            FVector Velocity(
                (*VelocityObj)->GetNumberField(TEXT("x")),
                (*VelocityObj)->GetNumberField(TEXT("y")),
                (*VelocityObj)->GetNumberField(TEXT("z"))
            );
            AddInitialVelocityModule(Emitter, Velocity);
            bSuccess = true;
        }
    }
    else if (ModuleType == TEXT("InitialColor"))
    {
        const TSharedPtr<FJsonObject>* ColorObj;
        if (JsonObject->TryGetObjectField(TEXT("color"), ColorObj))
        {
            FLinearColor Color(
                (*ColorObj)->GetNumberField(TEXT("r")),
                (*ColorObj)->GetNumberField(TEXT("g")),
                (*ColorObj)->GetNumberField(TEXT("b")),
                (*ColorObj)->GetNumberField(TEXT("a"))
            );
            AddInitialColorModule(Emitter, Color);
            bSuccess = true;
        }
    }
    else if (ModuleType == TEXT("InitialSize"))
    {
        const TSharedPtr<FJsonObject>* SizeObj;
        if (JsonObject->TryGetObjectField(TEXT("size"), SizeObj))
        {
            FVector2D Size(
                (*SizeObj)->GetNumberField(TEXT("x")),
                (*SizeObj)->GetNumberField(TEXT("y"))
            );
            AddInitialSizeModule(Emitter, Size);
            bSuccess = true;
        }
    }
    else if (ModuleType == TEXT("InitialLifetime"))
    {
        double Lifetime = JsonObject->GetNumberField(TEXT("lifetime"));
        AddLifetimeModule(Emitter, static_cast<float>(Lifetime));
        bSuccess = true;
    }

    if (bSuccess)
    {
        Emitter->MarkPackageDirty();
        return TEXT("{\"success\": true, \"message\": \"Module added successfully\"}");
    }
    else
    {
        return TEXT("{\"success\": false, \"error\": \"Unsupported module type or invalid parameters\"}");
    }
}

FString UnrealMCPNiagaraCommands::HandleSetNiagaraParameter(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    FString SystemPath = JsonObject->GetStringField(TEXT("system_path"));
    FString ParameterName = JsonObject->GetStringField(TEXT("parameter_name"));
    FString ParameterType = JsonObject->GetStringField(TEXT("parameter_type"));

    UNiagaraSystem* System = LoadNiagaraSystemFromPath(SystemPath);
    if (!System)
    {
        return TEXT("{\"success\": false, \"error\": \"System not found\"}");
    }

    // Create a Niagara component to set parameters
    UNiagaraComponent* NiagaraComponent = NewObject<UNiagaraComponent>();
    NiagaraComponent->SetAsset(System);

    // Set parameter based on type
    bool bSuccess = false;
    if (ParameterType == TEXT("float"))
    {
        double Value = JsonObject->GetNumberField(TEXT("value"));
        NiagaraComponent->SetFloatParameter(*ParameterName, static_cast<float>(Value));
        bSuccess = true;
    }
    else if (ParameterType == TEXT("vector"))
    {
        const TSharedPtr<FJsonObject>* VectorObj;
        if (JsonObject->TryGetObjectField(TEXT("value"), VectorObj))
        {
            FVector Vector(
                (*VectorObj)->GetNumberField(TEXT("x")),
                (*VectorObj)->GetNumberField(TEXT("y")),
                (*VectorObj)->GetNumberField(TEXT("z"))
            );
            NiagaraComponent->SetVectorParameter(*ParameterName, Vector);
            bSuccess = true;
        }
    }
    else if (ParameterType == TEXT("color"))
    {
        const TSharedPtr<FJsonObject>* ColorObj;
        if (JsonObject->TryGetObjectField(TEXT("value"), ColorObj))
        {
            FLinearColor Color(
                (*ColorObj)->GetNumberField(TEXT("r")),
                (*ColorObj)->GetNumberField(TEXT("g")),
                (*ColorObj)->GetNumberField(TEXT("b")),
                (*ColorObj)->GetNumberField(TEXT("a"))
            );
            NiagaraComponent->SetColorParameter(*ParameterName, Color);
            bSuccess = true;
        }
    }
    else if (ParameterType == TEXT("bool"))
    {
        bool Value = JsonObject->GetBoolField(TEXT("value"));
        NiagaraComponent->SetBoolParameter(*ParameterName, Value);
        bSuccess = true;
    }

    if (bSuccess)
    {
        return TEXT("{\"success\": true, \"message\": \"Parameter set successfully\"}");
    }
    else
    {
        return TEXT("{\"success\": false, \"error\": \"Unsupported parameter type or invalid value\"}");
    }
}

FString UnrealMCPNiagaraCommands::HandleSpawnNiagaraSystemAtLocation(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    FString SystemPath = JsonObject->GetStringField(TEXT("system_path"));

    const TSharedPtr<FJsonObject>* LocationObj;
    if (!JsonObject->TryGetObjectField(TEXT("location"), LocationObj))
    {
        return TEXT("{\"success\": false, \"error\": \"Missing location parameter\"}");
    }

    FVector Location(
        (*LocationObj)->GetNumberField(TEXT("x")),
        (*LocationObj)->GetNumberField(TEXT("y")),
        (*LocationObj)->GetNumberField(TEXT("z"))
    );

    UNiagaraSystem* System = LoadNiagaraSystemFromPath(SystemPath);
    if (!System)
    {
        return TEXT("{\"success\": false, \"error\": \"System not found\"}");
    }

    // Get current world
    UWorld* World = GEngine->GetWorldFromContextObject(nullptr, EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        return TEXT("{\"success\": false, \"error\": \"No valid world found\"}");
    }

    // Spawn Niagara system at location
    UNiagaraFunctionLibrary::SpawnSystemAtLocation(World, System, Location);

    return TEXT("{\"success\": true, \"message\": \"Niagara system spawned successfully\"}");
}

FString UnrealMCPNiagaraCommands::HandleCreateParticleLightEffect(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    FString EffectName = JsonObject->GetStringField(TEXT("effect_name"));
    FString PackagePath = JsonObject->GetStringField(TEXT("package_path"));

    // Create light particle emitter
    UNiagaraEmitter* LightEmitter = CreateNiagaraEmitterAsset(EffectName, PackagePath, TEXT("Light"));
    if (!LightEmitter)
    {
        return TEXT("{\"success\": false, \"error\": \"Failed to create light emitter\"}");
    }

    // Setup light renderer
    SetupLightRenderer(LightEmitter);

    // Add light-specific modules
    AddSpawnRateModule(LightEmitter, 5.0f);
    AddInitialColorModule(LightEmitter, FLinearColor::White);
    AddLifetimeModule(LightEmitter, 3.0f);

    // Save the emitter
    FString EmitterPackageName = NiagaraPathToPackageName(PackagePath + TEXT("/") + EffectName);
    if (!SaveNiagaraAsset(LightEmitter, EmitterPackageName))
    {
        return TEXT("{\"success\": false, \"error\": \"Failed to save light effect\"}");
    }

    return FString::Printf(TEXT("{\"success\": true, \"effect_path\": \"%s\"}"), *EmitterPackageName);
}

FString UnrealMCPNiagaraCommands::HandleCreateGPUSpriteEffect(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    FString EffectName = JsonObject->GetStringField(TEXT("effect_name"));
    FString PackagePath = JsonObject->GetStringField(TEXT("package_path"));

    // Create GPU sprite emitter
    UNiagaraEmitter* SpriteEmitter = CreateNiagaraEmitterAsset(EffectName, PackagePath, TEXT("GPU"));
    if (!SpriteEmitter)
    {
        return TEXT("{\"success\": false, \"error\": \"Failed to create GPU sprite emitter\"}");
    }

    // Setup sprite renderer
    SetupSpriteRenderer(SpriteEmitter);

    // Configure for GPU simulation
    // Note: SimTarget configuration is handled differently in UE 5.6

    // Add GPU-optimized modules
    AddSpawnRateModule(SpriteEmitter, 100.0f);
    AddInitialVelocityModule(SpriteEmitter, FVector(0, 0, 200));
    AddInitialColorModule(SpriteEmitter, FLinearColor::Blue);
    AddInitialSizeModule(SpriteEmitter, FVector2D(25, 25));
    AddLifetimeModule(SpriteEmitter, 2.0f);

    // Save the emitter
    FString EmitterPackageName = NiagaraPathToPackageName(PackagePath + TEXT("/") + EffectName);
    if (!SaveNiagaraAsset(SpriteEmitter, EmitterPackageName))
    {
        return TEXT("{\"success\": false, \"error\": \"Failed to save GPU sprite effect\"}");
    }

    return FString::Printf(TEXT("{\"success\": true, \"effect_path\": \"%s\"}"), *EmitterPackageName);
}

FString UnrealMCPNiagaraCommands::HandleCreateRibbonEffect(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    FString EffectName = JsonObject->GetStringField(TEXT("effect_name"));
    FString PackagePath = JsonObject->GetStringField(TEXT("package_path"));

    // Create ribbon emitter
    UNiagaraEmitter* RibbonEmitter = CreateNiagaraEmitterAsset(EffectName, PackagePath, TEXT("Ribbon"));
    if (!RibbonEmitter)
    {
        return TEXT("{\"success\": false, \"error\": \"Failed to create ribbon emitter\"}");
    }

    // Setup ribbon renderer
    SetupRibbonRenderer(RibbonEmitter);

    // Add ribbon-specific modules
    AddSpawnRateModule(RibbonEmitter, 20.0f);
    AddInitialVelocityModule(RibbonEmitter, FVector(100, 0, 0));
    AddInitialColorModule(RibbonEmitter, FLinearColor::Red);
    AddLifetimeModule(RibbonEmitter, 4.0f);

    // Save the emitter
    FString EmitterPackageName = NiagaraPathToPackageName(PackagePath + TEXT("/") + EffectName);
    if (!SaveNiagaraAsset(RibbonEmitter, EmitterPackageName))
    {
        return TEXT("{\"success\": false, \"error\": \"Failed to save ribbon effect\"}");
    }

    return FString::Printf(TEXT("{\"success\": true, \"effect_path\": \"%s\"}"), *EmitterPackageName);
}

FString UnrealMCPNiagaraCommands::HandleCreateMeshParticleEffect(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    FString EffectName = JsonObject->GetStringField(TEXT("effect_name"));
    FString PackagePath = JsonObject->GetStringField(TEXT("package_path"));
    FString MeshPath = JsonObject->GetStringField(TEXT("mesh_path"));

    // Load mesh if specified
    UStaticMesh* Mesh = nullptr;
    if (!MeshPath.IsEmpty())
    {
        Mesh = LoadObject<UStaticMesh>(nullptr, *MeshPath);
    }

    // Create mesh emitter
    UNiagaraEmitter* MeshEmitter = CreateNiagaraEmitterAsset(EffectName, PackagePath, TEXT("Mesh"));
    if (!MeshEmitter)
    {
        return TEXT("{\"success\": false, \"error\": \"Failed to create mesh emitter\"}");
    }

    // Setup mesh renderer
    SetupMeshRenderer(MeshEmitter, Mesh);

    // Add mesh-specific modules
    AddSpawnRateModule(MeshEmitter, 10.0f);
    AddInitialVelocityModule(MeshEmitter, FVector(0, 0, 150));
    AddInitialColorModule(MeshEmitter, FLinearColor::Green);
    AddLifetimeModule(MeshEmitter, 5.0f);

    // Save the emitter
    FString EmitterPackageName = NiagaraPathToPackageName(PackagePath + TEXT("/") + EffectName);
    if (!SaveNiagaraAsset(MeshEmitter, EmitterPackageName))
    {
        return TEXT("{\"success\": false, \"error\": \"Failed to save mesh particle effect\"}");
    }

    return FString::Printf(TEXT("{\"success\": true, \"effect_path\": \"%s\"}"), *EmitterPackageName);
}

FString UnrealMCPNiagaraCommands::HandleCreateBeamEffect(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    FString EffectName = JsonObject->GetStringField(TEXT("effect_name"));
    FString PackagePath = JsonObject->GetStringField(TEXT("package_path"));

    // Create beam emitter
    UNiagaraEmitter* BeamEmitter = CreateNiagaraEmitterAsset(EffectName, PackagePath, TEXT("Beam"));
    if (!BeamEmitter)
    {
        return TEXT("{\"success\": false, \"error\": \"Failed to create beam emitter\"}");
    }

    // Setup ribbon renderer for beam effect
    SetupRibbonRenderer(BeamEmitter);

    // Add beam-specific modules
    AddSpawnRateModule(BeamEmitter, 1.0f); // Beams typically spawn once
    AddInitialColorModule(BeamEmitter, FLinearColor::Yellow);
    AddLifetimeModule(BeamEmitter, 1.0f);

    // Save the emitter
    FString EmitterPackageName = NiagaraPathToPackageName(PackagePath + TEXT("/") + EffectName);
    if (!SaveNiagaraAsset(BeamEmitter, EmitterPackageName))
    {
        return TEXT("{\"success\": false, \"error\": \"Failed to save beam effect\"}");
    }

    return FString::Printf(TEXT("{\"success\": true, \"effect_path\": \"%s\"}"), *EmitterPackageName);
}

FString UnrealMCPNiagaraCommands::HandleOptimizeNiagaraPerformance(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    FString SystemPath = JsonObject->GetStringField(TEXT("system_path"));
    FString OptimizationLevel = JsonObject->GetStringField(TEXT("optimization_level"));

    UNiagaraSystem* System = LoadNiagaraSystemFromPath(SystemPath);
    if (!System)
    {
        return TEXT("{\"success\": false, \"error\": \"System not found\"}");
    }

    // Apply optimization based on level
    if (OptimizationLevel == TEXT("Low"))
    {
        // Basic optimizations
        System->SetWarmupTime(0.0f);
    }
    else if (OptimizationLevel == TEXT("Medium"))
    {
        // Moderate optimizations
        System->SetWarmupTime(1.0f);
        System->bFixedBounds = true;
    }
    else if (OptimizationLevel == TEXT("High"))
    {
        // Aggressive optimizations
        System->SetWarmupTime(2.0f);
        System->bFixedBounds = true;
    }

    System->MarkPackageDirty();

    return TEXT("{\"success\": true, \"message\": \"Niagara system optimized successfully\"}");
}
