// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "GameFramework/GameModeBase.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/SpectatorPawn.h"
#include "Engine/NetDriver.h"
#include "Engine/NetConnection.h"
#include "Net/UnrealNetwork.h"
#include "Net/RepLayout.h"
#include "Net/DataReplication.h"
#include "ReplicationGraph.h"
#include "Iris/ReplicationSystem/ReplicationSystem.h"
#include "OnlineSubsystem.h"
#include "OnlineSessionSettings.h"
#include "Interfaces/OnlineSessionInterface.h"
#include "Kismet/GameplayStatics.h"
#include "HAL/IConsoleManager.h"
#include "Stats/Stats.h"
#include "Net/NetworkProfiler.h"

/**
 * UnrealMCPNetworkingCommands
 * 
 * Handles all Networking related commands for the MCP server.
 * Provides comprehensive multiplayer networking tools and configuration
 * compatible with Unreal Engine 5.6 networking systems.
 * 
 * Features:
 * - Multiplayer session management
 * - Actor replication configuration
 * - Remote Procedure Call (RPC) creation
 * - Network settings configuration
 * - Game Mode networking setup
 * - Player connection management
 * - Replication Graph optimization
 * - Network debugging and simulation
 * - Performance analysis and profiling
 * - Cross-platform networking support
 */
class UNREALMCP_API UnrealMCPNetworkingCommands
{
public:
    UnrealMCPNetworkingCommands();
    ~UnrealMCPNetworkingCommands();

    // Core Networking Functions
    static FString HandleCreateMultiplayerSession(const FString& JsonParams);
    static FString HandleSetupActorReplication(const FString& JsonParams);
    static FString HandleCreateRPCFunction(const FString& JsonParams);
    static FString HandleConfigureNetworkSettings(const FString& JsonParams);

    // Game Mode and Player Management
    static FString HandleSetupGameModeNetworking(const FString& JsonParams);
    static FString HandleManagePlayerConnections(const FString& JsonParams);

    // Advanced Networking Features
    static FString HandleConfigureReplicationGraph(const FString& JsonParams);
    static FString HandleSetupNetworkDebugging(const FString& JsonParams);
    static FString HandleAnalyzeNetworkPerformance(const FString& JsonParams);

private:
    // Helper Functions for Session Management
    static bool CreateOnlineSession(const FString& SessionName, int32 MaxPlayers, bool bIsLAN, 
        bool bIsDedicated, bool bAllowJoinInProgress, bool bUsePresence);
    static bool DestroyOnlineSession(const FString& SessionName);
    static bool StartOnlineSession(const FString& SessionName);
    static bool EndOnlineSession(const FString& SessionName);
    static TArray<FOnlineSessionSearchResult> FindOnlineSessions(bool bIsLAN, int32 MaxResults);
    
    // Helper Functions for Actor Replication
    static bool SetupActorForReplication(const FString& ActorClassPath, bool bReplicateMovement, 
        const TArray<FString>& ReplicateProperties, float NetCullDistance, float NetUpdateFrequency);
    static bool EnableActorReplication(AActor* Actor, bool bEnable);
    static bool ConfigureActorNetworkSettings(AActor* Actor, float CullDistance, float UpdateFrequency);
    static bool AddReplicatedProperty(UClass* ActorClass, const FString& PropertyName);
    static bool SetupMovementReplication(AActor* Actor, bool bEnable);
    
    // Helper Functions for RPC Creation
    static bool CreateServerRPC(UClass* TargetClass, const FString& FunctionName, bool bIsReliable, 
        const TArray<TPair<FString, FString>>& Parameters);
    static bool CreateClientRPC(UClass* TargetClass, const FString& FunctionName, bool bIsReliable, 
        const TArray<TPair<FString, FString>>& Parameters);
    static bool CreateNetMulticastRPC(UClass* TargetClass, const FString& FunctionName, bool bIsReliable, 
        const TArray<TPair<FString, FString>>& Parameters);
    static bool ValidateRPCParameters(const TArray<TPair<FString, FString>>& Parameters);
    
    // Helper Functions for Network Configuration
    static bool SetNetworkTickRate(int32 TickRate);
    static bool SetMaxClientRate(int32 MaxInternetClientRate, int32 MaxClientRate);
    static bool EnableNetworkProfiler(bool bEnable);
    static bool ConfigureNetworkEmulation(const TMap<FString, float>& EmulationSettings);
    static bool ApplyNetworkSettings();
    
    // Helper Functions for Game Mode Setup
    static bool ConfigureGameModeForNetworking(const FString& GameModeClassPath, 
        const FString& PlayerControllerClassPath, const FString& PawnClassPath, 
        const FString& SpectatorClassPath, bool bEnableSeamlessTravel);
    static bool SetDefaultGameModeClasses(UClass* GameModeClass, UClass* PlayerControllerClass, 
        UClass* PawnClass, UClass* SpectatorClass);
    static bool EnableSeamlessTravel(bool bEnable);
    
    // Helper Functions for Player Management
    static TArray<TSharedPtr<FJsonObject>> GetConnectedPlayers();
    static bool KickPlayer(const FString& PlayerID, const FString& Reason);
    static bool BanPlayer(const FString& PlayerID, const FString& Reason, int32 DurationMinutes);
    static bool UnbanPlayer(const FString& PlayerID);
    static bool IsPlayerBanned(const FString& PlayerID);
    
    // Helper Functions for Replication Graph
    static bool EnableReplicationGraph(bool bEnable, float SpatialBias, int32 MaxActorsPerFrame);
    static bool ConfigureIrisReplication(bool bEnable);
    static bool AddCustomReplicationNodes(const TArray<FString>& NodeClassPaths);
    static bool SetReplicationGraphSettings(const TMap<FString, float>& Settings);
    
    // Helper Functions for Network Debugging
    static bool EnableNetworkLogging(bool bEnable, const FString& LogLevel);
    static bool SetupPacketLossSimulation(bool bEnable, float LossPercentage);
    static bool SetupLatencySimulation(int32 LatencyMS);
    static bool EnableNetworkStats(bool bEnable);
    static TSharedPtr<FJsonObject> GetNetworkDebugInfo();
    
    // Helper Functions for Performance Analysis
    static TSharedPtr<FJsonObject> CollectBandwidthStats(float Duration);
    static TSharedPtr<FJsonObject> CollectLatencyStats(float Duration);
    static TSharedPtr<FJsonObject> CollectPacketLossStats(float Duration);
    static TSharedPtr<FJsonObject> GenerateNetworkPerformanceReport(const TSharedPtr<FJsonObject>& Stats);
    static bool SaveNetworkPerformanceReport(const TSharedPtr<FJsonObject>& Report, const FString& Filename);
    
    // Utility Functions
    static UWorld* GetNetworkWorld();
    static UNetDriver* GetNetDriver();
    static IOnlineSubsystem* GetOnlineSubsystem();
    static IOnlineSessionPtr GetSessionInterface();
    static UClass* LoadClassFromPath(const FString& ClassPath);
    static bool IsValidNetworkClass(UClass* Class);
    static FString GetPlayerIDFromController(APlayerController* Controller);
    
    // JSON Parsing Helpers
    static TSharedPtr<FJsonObject> ParseJsonParams(const FString& JsonParams);
    static FString CreateJsonResponse(bool bSuccess, const FString& Message, 
        const TMap<FString, FString>& AdditionalData = TMap<FString, FString>());
    static FString CreateJsonResponse(bool bSuccess, const FString& Message, 
        const TSharedPtr<FJsonObject>& DataObject);
    
    // Validation Functions
    static bool ValidateSessionName(const FString& SessionName);
    static bool ValidateMaxPlayers(int32 MaxPlayers);
    static bool ValidateRPCType(const FString& RPCType);
    static bool ValidateLogLevel(const FString& LogLevel);
    static bool ValidatePlayerAction(const FString& Action);
    static bool ValidateNetworkSettings(int32 TickRate, int32 MaxClientRate);
    
    // Constants for Networking
    static const TArray<FString> SupportedRPCTypes;
    static const TArray<FString> SupportedLogLevels;
    static const TArray<FString> SupportedPlayerActions;
    static const TArray<FString> SupportedParameterTypes;
    
    // Networking Constants
    static const int32 DefaultMaxPlayers;
    static const int32 DefaultTickRate;
    static const int32 DefaultMaxClientRate;
    static const float DefaultNetCullDistance;
    static const float DefaultNetUpdateFrequency;
    static const float DefaultSpatialBias;
    static const int32 DefaultMaxActorsPerFrame;
    static const float MaxAnalysisDuration;
};
