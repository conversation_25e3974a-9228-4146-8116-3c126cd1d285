// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Engine/RendererSettings.h"
#include "Engine/PostProcessVolume.h"
#include "Components/PostProcessComponent.h"
#include "RenderingThread.h"
#include "RHI.h"
#include "RHICommandList.h"
#include "GlobalShader.h"
#include "ShaderParameterStruct.h"
#include "ScreenPass.h"
#include "FinalPostProcessSettings.h"
#include "LumenVisualizationData.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "SystemSettings.h"
#include "GameFramework/GameUserSettings.h"

/**
 * UnrealMCPRenderingPipelineCommands
 * 
 * Handles all Rendering Pipeline related commands for the MCP server.
 * Provides comprehensive rendering configuration, optimization, and analysis tools
 * compatible with Unreal Engine 5.6 rendering pipeline.
 * 
 * Features:
 * - Lumen Global Illumination configuration
 * - Nanite Virtualized Geometry setup
 * - Virtual Shadow Maps management
 * - Temporal Super Resolution (TSR) configuration
 * - Hardware Ray Tracing setup
 * - MegaLights system configuration
 * - Post Process Pipeline management
 * - Rendering scalability and optimization
 * - Performance analysis and profiling
 * - Cross-platform rendering configuration
 */
class UNREALMCP_API UnrealMCPRenderingPipelineCommands
{
public:
    UnrealMCPRenderingPipelineCommands();
    ~UnrealMCPRenderingPipelineCommands();

    // Core Rendering Pipeline Configuration
    static FString HandleConfigureLumenGlobalIllumination(const FString& JsonParams);
    static FString HandleConfigureNaniteVirtualizedGeometry(const FString& JsonParams);
    static FString HandleConfigureVirtualShadowMaps(const FString& JsonParams);
    static FString HandleSetupTemporalSuperResolution(const FString& JsonParams);
    static FString HandleConfigureHardwareRayTracing(const FString& JsonParams);
    static FString HandleSetupMegaLightsSystem(const FString& JsonParams);

    // Post Processing and Effects
    static FString HandleConfigurePostProcessPipeline(const FString& JsonParams);
    static FString HandleSetupRenderingScalability(const FString& JsonParams);

    // Performance and Optimization
    static FString HandleOptimizeRenderingPerformance(const FString& JsonParams);
    static FString HandleAnalyzeRenderingMetrics(const FString& JsonParams);
    static FString HandleConfigureRenderingForPlatform(const FString& JsonParams);
    static FString HandleSetupRenderingDebugTools(const FString& JsonParams);

private:
    // Helper Functions for Lumen Configuration
    static bool ConfigureLumenSettings(bool bEnableLumen, int32 GIQuality, int32 ReflectionQuality, 
        bool bEnableHardwareRayTracing, int32 SurfaceCacheResolution);
    static bool SetLumenGlobalIlluminationQuality(int32 Quality);
    static bool SetLumenReflectionQuality(int32 Quality);
    static bool EnableLumenHardwareRayTracing(bool bEnable);
    static bool ConfigureLumenSurfaceCache(int32 Resolution);
    
    // Helper Functions for Nanite Configuration
    static bool ConfigureNaniteSettings(bool bEnableNanite, int32 ClusterPerPage, 
        int32 MaxCandidateClusters, int32 MaxNodes, bool bEnableTessellation);
    static bool SetNaniteClusterSettings(int32 ClusterPerPage, int32 MaxCandidateClusters);
    static bool SetNaniteMemorySettings(int32 MaxNodes);
    static bool EnableNaniteTessellation(bool bEnable);
    
    // Helper Functions for Virtual Shadow Maps Configuration
    static bool ConfigureVSMSettings(bool bEnableVSM, int32 MaxPhysicalPages, 
        float ResolutionLodBiasDirectional, float ResolutionLodBiasLocal, bool bEnableCaching);
    static bool SetVSMPagePoolSize(int32 MaxPhysicalPages);
    static bool SetVSMResolutionBias(float DirectionalBias, float LocalBias);
    static bool EnableVSMCaching(bool bEnable);
    
    // Helper Functions for Temporal Super Resolution
    static bool ConfigureTSRSettings(bool bEnableTSR, int32 QualityLevel, float Sharpness, 
        bool bEnableAlphaChannel, int32 HistorySampleCount);
    static bool SetTSRQuality(int32 QualityLevel);
    static bool SetTSRSharpness(float Sharpness);
    static bool EnableTSRAlphaChannel(bool bEnable);
    
    // Helper Functions for Hardware Ray Tracing
    static bool ConfigureRayTracingSettings(bool bEnableRayTracing, bool bEnableReflections, 
        bool bEnableShadows, bool bEnableGlobalIllumination, int32 MaxRecursionDepth);
    static bool EnableRayTracedReflections(bool bEnable);
    static bool EnableRayTracedShadows(bool bEnable);
    static bool EnableRayTracedGlobalIllumination(bool bEnable);
    static bool SetRayTracingRecursionDepth(int32 MaxDepth);
    
    // Helper Functions for MegaLights System
    static bool ConfigureMegaLightsSettings(bool bEnableMegaLights, const FString& ShadowMethod, 
        int32 MaxLightsPerTile, bool bEnableAreaShadows, int32 LightCullingTileSize);
    static bool SetMegaLightsShadowMethod(const FString& ShadowMethod);
    static bool SetMegaLightsLightCulling(int32 MaxLightsPerTile, int32 TileSize);
    static bool EnableMegaLightsAreaShadows(bool bEnable);
    
    // Helper Functions for Post Process Pipeline
    static bool ConfigurePostProcessSettings(bool bEnableBloom, bool bEnableAutoExposure, 
        bool bEnableToneMapping, const FString& ToneMapperType, float ExposureCompensation);
    static bool SetPostProcessBloom(bool bEnable);
    static bool SetPostProcessAutoExposure(bool bEnable);
    static bool SetPostProcessToneMapping(bool bEnable, const FString& ToneMapperType);
    static bool SetPostProcessExposure(float ExposureCompensation);
    
    // Helper Functions for Rendering Scalability
    static bool ConfigureScalabilitySettings(const FString& ScalabilityGroup, float ViewDistanceScale, 
        int32 AntiAliasingQuality, int32 ShadowQuality, int32 PostProcessQuality);
    static bool SetScalabilityGroup(const FString& Group);
    static bool SetViewDistanceScale(float Scale);
    static bool SetRenderingQualityLevels(int32 AAQuality, int32 ShadowQuality, int32 PPQuality);
    
    // Helper Functions for Performance Optimization
    static bool OptimizeForPlatform(const FString& Platform, int32 TargetFPS, 
        const FString& OptimizationLevel);
    static bool EnableGPUProfiling(bool bEnable);
    static bool EnableStatCommands(bool bEnable);
    static bool ApplyPerformancePreset(const FString& OptimizationLevel);
    
    // Helper Functions for Metrics Analysis
    static TSharedPtr<FJsonObject> CollectRenderingMetrics(float Duration, bool bIncludeGPUStats, 
        bool bIncludeDrawCalls, bool bIncludeMemoryUsage);
    static TSharedPtr<FJsonObject> AnalyzeGPUPerformance();
    static TSharedPtr<FJsonObject> AnalyzeDrawCallStats();
    static TSharedPtr<FJsonObject> AnalyzeMemoryUsage();
    static TSharedPtr<FJsonObject> GenerateRenderingReport(const TSharedPtr<FJsonObject>& Metrics);
    
    // Utility Functions
    static URendererSettings* GetRendererSettings();
    static UGameUserSettings* GetGameUserSettings();
    static bool ApplyRenderingSettings();
    static bool ValidateRenderingConfiguration();
    static FString GetRenderingErrorMessage(const FString& ErrorCode);
    
    // JSON Parsing Helpers
    static TSharedPtr<FJsonObject> ParseJsonParams(const FString& JsonParams);
    static FString CreateJsonResponse(bool bSuccess, const FString& Message, 
        const TMap<FString, FString>& AdditionalData = TMap<FString, FString>());
    
    // Validation Functions
    static bool ValidateQualityLevel(int32 Quality, int32 MaxLevel = 4);
    static bool ValidateShadowMethod(const FString& ShadowMethod);
    static bool ValidateToneMapperType(const FString& ToneMapperType);
    static bool ValidateScalabilityGroup(const FString& ScalabilityGroup);
    static bool ValidateOptimizationLevel(const FString& OptimizationLevel);
    static bool ValidatePlatform(const FString& Platform);
    
    // Constants for Rendering Pipeline
    static const TArray<FString> SupportedShadowMethods;
    static const TArray<FString> SupportedToneMapperTypes;
    static const TArray<FString> SupportedScalabilityGroups;
    static const TArray<FString> SupportedOptimizationLevels;
    static const TArray<FString> SupportedPlatforms;
    static const TArray<FString> SupportedOutputFormats;
    
    // Quality Level Constants
    static const int32 MaxQualityLevel;
    static const int32 MaxTSRQualityLevel;
    static const int32 MaxRayTracingRecursionDepth;
};
