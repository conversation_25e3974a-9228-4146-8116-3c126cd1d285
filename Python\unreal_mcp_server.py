"""
Unreal Engine MCP Server

A simple MCP server for interacting with Unreal Engine.
"""

import logging
import socket
import sys
import json
from contextlib import asynccontextmanager
from typing import AsyncIterator, Dict, Any, Optional
from mcp.server.fastmcp import FastMCP

# Configure logging with more detailed format
logging.basicConfig(
    level=logging.DEBUG,  # Change to DEBUG level for more details
    format='%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
    handlers=[
        logging.FileHandler('unreal_mcp.log'),
        # logging.StreamHandler(sys.stdout) # Remove this handler to unexpected non-whitespace characters in JSON
    ]
)
logger = logging.getLogger("UnrealMCP")

# Configuration
UNREAL_HOST = "127.0.0.1"
UNREAL_PORT = 55557

class UnrealConnection:
    """Connection to an Unreal Engine instance."""
    
    def __init__(self):
        """Initialize the connection."""
        self.socket = None
        self.connected = False
    
    def connect(self) -> bool:
        """Connect to the Unreal Engine instance."""
        try:
            # Close any existing socket
            if self.socket:
                try:
                    self.socket.close()
                except:
                    pass
                self.socket = None
            
            logger.info(f"Connecting to Unreal at {UNREAL_HOST}:{UNREAL_PORT}...")
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(5)  # 5 second timeout
            
            # Set socket options for better stability
            self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
            
            # Set larger buffer sizes
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 65536)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 65536)
            
            self.socket.connect((UNREAL_HOST, UNREAL_PORT))
            self.connected = True
            logger.info("Connected to Unreal Engine")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to Unreal: {e}")
            self.connected = False
            return False
    
    def disconnect(self):
        """Disconnect from the Unreal Engine instance."""
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
        self.socket = None
        self.connected = False

    def receive_full_response(self, sock, buffer_size=4096) -> bytes:
        """Receive a complete response from Unreal, handling chunked data."""
        chunks = []
        sock.settimeout(5)  # 5 second timeout
        try:
            while True:
                chunk = sock.recv(buffer_size)
                if not chunk:
                    if not chunks:
                        raise Exception("Connection closed before receiving data")
                    break
                chunks.append(chunk)
                
                # Process the data received so far
                data = b''.join(chunks)
                decoded_data = data.decode('utf-8')
                
                # Try to parse as JSON to check if complete
                try:
                    json.loads(decoded_data)
                    logger.info(f"Received complete response ({len(data)} bytes)")
                    return data
                except json.JSONDecodeError:
                    # Not complete JSON yet, continue reading
                    logger.debug(f"Received partial response, waiting for more data...")
                    continue
                except Exception as e:
                    logger.warning(f"Error processing response chunk: {str(e)}")
                    continue
        except socket.timeout:
            logger.warning("Socket timeout during receive")
            if chunks:
                # If we have some data already, try to use it
                data = b''.join(chunks)
                try:
                    json.loads(data.decode('utf-8'))
                    logger.info(f"Using partial response after timeout ({len(data)} bytes)")
                    return data
                except:
                    pass
            raise Exception("Timeout receiving Unreal response")
        except Exception as e:
            logger.error(f"Error during receive: {str(e)}")
            raise
    
    def send_command(self, command: str, params: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """Send a command to Unreal Engine and get the response."""
        # Always reconnect for each command, since Unreal closes the connection after each command
        # This is different from Unity which keeps connections alive
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
            self.connected = False
        
        if not self.connect():
            logger.error("Failed to connect to Unreal Engine for command")
            return None
        
        try:
            # Match Unity's command format exactly
            command_obj = {
                "type": command,  # Use "type" instead of "command"
                "params": params or {}  # Use Unity's params or {} pattern
            }
            
            # Send without newline, exactly like Unity
            command_json = json.dumps(command_obj)
            logger.info(f"Sending command: {command_json}")
            self.socket.sendall(command_json.encode('utf-8'))
            
            # Read response using improved handler
            response_data = self.receive_full_response(self.socket)
            response = json.loads(response_data.decode('utf-8'))
            
            # Log complete response for debugging
            logger.info(f"Complete response from Unreal: {response}")
            
            # Check for both error formats: {"status": "error", ...} and {"success": false, ...}
            if response.get("status") == "error":
                error_message = response.get("error") or response.get("message", "Unknown Unreal error")
                logger.error(f"Unreal error (status=error): {error_message}")
                # We want to preserve the original error structure but ensure error is accessible
                if "error" not in response:
                    response["error"] = error_message
            elif response.get("success") is False:
                # This format uses {"success": false, "error": "message"} or {"success": false, "message": "message"}
                error_message = response.get("error") or response.get("message", "Unknown Unreal error")
                logger.error(f"Unreal error (success=false): {error_message}")
                # Convert to the standard format expected by higher layers
                response = {
                    "status": "error",
                    "error": error_message
                }
            
            # Always close the connection after command is complete
            # since Unreal will close it on its side anyway
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
            self.connected = False
            
            return response
            
        except Exception as e:
            logger.error(f"Error sending command: {e}")
            # Always reset connection state on any error
            self.connected = False
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
            return {
                "status": "error",
                "error": str(e)
            }

# Global connection state
_unreal_connection: UnrealConnection = None

def get_unreal_connection() -> Optional[UnrealConnection]:
    """Get the connection to Unreal Engine."""
    global _unreal_connection
    try:
        if _unreal_connection is None:
            _unreal_connection = UnrealConnection()
            if not _unreal_connection.connect():
                logger.warning("Could not connect to Unreal Engine")
                _unreal_connection = None
        else:
            # Verify connection is still valid with a ping-like test
            try:
                # Simple test by sending an empty buffer to check if socket is still connected
                _unreal_connection.socket.sendall(b'\x00')
                logger.debug("Connection verified with ping test")
            except Exception as e:
                logger.warning(f"Existing connection failed: {e}")
                _unreal_connection.disconnect()
                _unreal_connection = None
                # Try to reconnect
                _unreal_connection = UnrealConnection()
                if not _unreal_connection.connect():
                    logger.warning("Could not reconnect to Unreal Engine")
                    _unreal_connection = None
                else:
                    logger.info("Successfully reconnected to Unreal Engine")
        
        return _unreal_connection
    except Exception as e:
        logger.error(f"Error getting Unreal connection: {e}")
        return None

@asynccontextmanager
async def server_lifespan(server: FastMCP) -> AsyncIterator[Dict[str, Any]]:
    """Handle server startup and shutdown."""
    global _unreal_connection
    logger.info("UnrealMCP server starting up")
    try:
        _unreal_connection = get_unreal_connection()
        if _unreal_connection:
            logger.info("Connected to Unreal Engine on startup")
        else:
            logger.warning("Could not connect to Unreal Engine on startup")
    except Exception as e:
        logger.error(f"Error connecting to Unreal Engine on startup: {e}")
        _unreal_connection = None
    
    try:
        yield {}
    finally:
        if _unreal_connection:
            _unreal_connection.disconnect()
            _unreal_connection = None
        logger.info("Unreal MCP server shut down")

# Initialize server
mcp = FastMCP("UnrealMCP")

# Import and register tools
from tools.editor_tools import register_editor_tools
from tools.blueprint_tools import register_blueprint_tools
from tools.node_tools import register_node_tools
from tools.project_tools import register_project_tools
from tools.umg_tools import register_umg_tools
from tools.collision_tools import register_collision_tools
from tools.network_tools import register_network_tools
from tools.ai_tools import register_ai_tools
from tools.material_tools import register_material_tools
from tools.niagara_tools import register_niagara_tools
from tools.chaos_physics_tools import register_chaos_physics_tools
from tools.rendering_pipeline_tools import register_rendering_pipeline_tools
from tools.hardware_detection_tools import register_hardware_detection_tools
from tools.audio_system_tools import register_audio_system_tools
from tools.networking_tools import register_networking_tools
from tools.platform_tools import register_platform_tools
from tools.performance_tools import register_performance_tools
from tools.pathfinding_tools import register_pathfinding_tools
from tools.procedural_tools import register_procedural_tools
from tools.vision_tools import register_vision_tools
from tools.world_partition_tools import register_world_partition_tools
from tools.realm_transition_tools import register_realm_transition_tools

# Register all tools
register_editor_tools(mcp)
register_blueprint_tools(mcp)
register_node_tools(mcp)
register_project_tools(mcp)
register_umg_tools(mcp)
register_collision_tools(mcp)
register_network_tools(mcp)
register_ai_tools(mcp)
register_material_tools(mcp)
register_niagara_tools(mcp)
register_chaos_physics_tools(mcp)
register_rendering_pipeline_tools(mcp)
register_hardware_detection_tools(mcp)
register_audio_system_tools(mcp)
register_networking_tools(mcp)
register_platform_tools(mcp)
register_performance_tools(mcp)
register_pathfinding_tools(mcp)
register_procedural_tools(mcp)
register_vision_tools(mcp)
register_world_partition_tools(mcp)
register_realm_transition_tools(mcp)  

@mcp.prompt()
def info():
    """Information about available Unreal MCP tools and best practices."""
    return """
    # Unreal MCP Server Tools and Best Practices
    
    ## UMG (Widget Blueprint) Tools
    - `create_umg_widget_blueprint(widget_name, parent_class="UserWidget", path="/Game/UI")` 
      Create a new UMG Widget Blueprint
    - `add_text_block_to_widget(widget_name, text_block_name, text="", position=[0,0], size=[200,50], font_size=12, color=[1,1,1,1])`
      Add a Text Block widget with customizable properties
    - `add_button_to_widget(widget_name, button_name, text="", position=[0,0], size=[200,50], font_size=12, color=[1,1,1,1], background_color=[0.1,0.1,0.1,1])`
      Add a Button widget with text and styling
    - `bind_widget_event(widget_name, widget_component_name, event_name, function_name="")`
      Bind events like OnClicked to functions
    - `add_widget_to_viewport(widget_name, z_order=0)`
      Add widget instance to game viewport
    - `set_text_block_binding(widget_name, text_block_name, binding_property, binding_type="Text")`
      Set up dynamic property binding for text blocks

    ## Editor Tools
    ### Viewport and Screenshots
    - `focus_viewport(target, location, distance, orientation)` - Focus viewport
    - `take_screenshot(filename, show_ui, resolution)` - Capture screenshots

    ### Actor Management
    - `get_actors_in_level()` - List all actors in current level
    - `find_actors_by_name(pattern)` - Find actors by name pattern
    - `spawn_actor(name, type, location=[0,0,0], rotation=[0,0,0], scale=[1,1,1])` - Create actors
    - `delete_actor(name)` - Remove actors
    - `set_actor_transform(name, location, rotation, scale)` - Modify actor transform
    - `get_actor_properties(name)` - Get actor properties
    
    ## Blueprint Management
    - `create_blueprint(name, parent_class)` - Create new Blueprint classes
    - `add_component_to_blueprint(blueprint_name, component_type, component_name)` - Add components
    - `set_static_mesh_properties(blueprint_name, component_name, static_mesh)` - Configure meshes
    - `set_physics_properties(blueprint_name, component_name)` - Configure physics
    - `compile_blueprint(blueprint_name)` - Compile Blueprint changes
    - `set_blueprint_property(blueprint_name, property_name, property_value)` - Set properties
    - `set_pawn_properties(blueprint_name)` - Configure Pawn settings
    - `spawn_blueprint_actor(blueprint_name, actor_name)` - Spawn Blueprint actors
    
    ## Blueprint Node Management
    - `add_blueprint_event_node(blueprint_name, event_type)` - Add event nodes
    - `add_blueprint_input_action_node(blueprint_name, action_name)` - Add input nodes
    - `add_blueprint_function_node(blueprint_name, target, function_name)` - Add function nodes
    - `connect_blueprint_nodes(blueprint_name, source_node_id, source_pin, target_node_id, target_pin)` - Connect nodes
    - `add_blueprint_variable(blueprint_name, variable_name, variable_type)` - Add variables
    - `add_blueprint_get_self_component_reference(blueprint_name, component_name)` - Add component refs
    - `add_blueprint_self_reference(blueprint_name)` - Add self references
    - `find_blueprint_nodes(blueprint_name, node_type, event_type)` - Find nodes
    
    ## Project Tools
    - `create_input_mapping(action_name, key, input_type)` - Create input mappings
    
    ## Collision System Tools
    - `create_collision_channel(channel_name, channel_type="ECR_Block", description="")` - Create custom collision channels
    - `configure_collision_profile(profile_name, collision_settings)` - Configure collision profiles with channel responses
    - `set_layer_collision_rules(layer_name, collision_rules)` - Set collision rules for specific layers
    - `configure_collision_size_scaling(layer_name, scale_factor, size_rules)` - Configure collision size scaling
    - `create_layer_collision_matrix(layers, interaction_matrix)` - Create collision interaction matrix between layers
    - `optimize_collision_detection(layer_name, optimization_settings)` - Optimize collision detection for layers
    - `get_collision_system_status()` - Get current collision system status
    - `configure_collision_complexity(actor_name, complexity_type, custom_settings)` - Configure actor collision complexity
    - `setup_layer_collision_filtering(layer_name, filter_settings)` - Setup collision filtering for specific layers
    
    ### Collision System Best Practices:
    
    **Collision Channel Types:**
    - Use "ECR_Block" for solid objects that block movement
    - Use "ECR_Overlap" for trigger zones and detection areas
    - Use "ECR_Ignore" for objects that should not collide
    - Create custom channels for specific gameplay mechanics
    
    **Collision Complexity Types:**
    - "Simple" - Use simple collision shapes (boxes, spheres, capsules)
    - "Complex" - Use complex mesh collision for detailed interactions
    - "UseSimpleAsComplex" - Use simple collision for both simple and complex queries
    - "UseComplexAsSimple" - Use complex collision for both simple and complex queries
    
    **Layer Configuration Guidelines:**
    - Define clear layer hierarchies (Player, Environment, Projectiles, etc.)
    - Set appropriate collision rules between layers
    - Use size scaling for different detail levels
    - Configure filtering to optimize performance
    
    **Performance Optimization:**
    - Use simple collision shapes whenever possible
    - Limit the number of collision channels (max 32)
    - Configure appropriate collision complexity for each actor type
    - Use layer filtering to reduce unnecessary collision checks
    - Monitor collision performance with `get_collision_system_status()`
    
    **Multi-Layer System Setup:**
    ```json
    {
      "layer_name": "gameplay_layer",
      "collision_rules": {
        "player_channel": "ECR_Block",
        "environment_channel": "ECR_Block",
        "projectile_channel": "ECR_Overlap"
      },
      "scale_factor": 1.0,
      "optimization_settings": {
        "enable_ccd": true,
        "max_collision_iterations": 4
      }
    }
    ```
    
    ## Network System Tools
    - `create_multilayer_network_system(layer_configs, global_settings)` - Create multilayer network systems
    - `configure_object_replication(layer_name, replication_configs)` - Configure object replication for network layers
    - `setup_client_prediction(layer_name, prediction_configs)` - Setup client prediction systems
    - `configure_network_synchronization(layer_name, sync_configs)` - Configure network synchronization
    - `setup_lag_compensation(layer_name, compensation_settings)` - Setup lag compensation
    - `configure_bandwidth_optimization(layer_name, optimization_configs)` - Configure bandwidth optimization
    - `debug_network_performance(layer_name, debug_configs)` - Debug network performance
    - `validate_network_setup(layer_name, validation_configs)` - Validate network setup
    - `get_network_system_status(layer_name)` - Get network system status
    
    ## AI System Tools
    - `create_ai_learning_pipeline(layer_name, learning_type="reinforcement", model_config=None, training_data_path="")` - Create adaptive AI learning systems
    - `configure_adaptive_behavior(layer_name, behavior_type, adaptation_rules=None, response_threshold=0.7)` - Configure NPC adaptive behaviors
    - `setup_dynamic_spawn_system(layer_name, spawn_rules=None, difficulty_scaling=None, population_limits=None)` - Setup AI-driven dynamic spawning
    - `create_ai_decision_tree(layer_name, tree_name, decision_nodes=None, learning_enabled=True)` - Create adaptive decision trees
    - `configure_special_events(layer_name, event_triggers=None, event_responses=None, adaptive_scaling=True)` - Configure adaptive special events
    - `setup_ai_communication_system(layer_name, communication_range=1000.0, message_types=None, learning_from_communication=True)` - Setup NPC communication
    - `configure_player_profiling(layer_name, profiling_metrics=None, adaptation_speed=0.1, profile_persistence=True)` - Configure player behavior profiling
    - `setup_ai_memory_system(layer_name, memory_type, memory_capacity=100, retention_time=300.0)` - Setup AI memory systems
    - `optimize_ai_performance(layer_name, optimization_level="balanced", performance_metrics=None)` - Optimize AI performance
    - `debug_ai_system(layer_name, debug_level="info", debug_components=None)` - Debug AI systems
    - `validate_ai_setup(layer_name, validation_tests=None)` - Validate AI system configuration
    - `get_ai_system_status(layer_name=None)` - Get AI system status and metrics
    
    ## World Partition Tools
    - `create_world_partition_level(level_name, layer_type="Firmamento", grid_size=25600, cell_size=1600)` - Create new levels with World Partition for multilayers
    - `configure_streaming_cell(level_name, cell_x, cell_y, layer_type, streaming_distance=5000.0, priority=1)` - Configure specific streaming cells
    - `create_layer_hierarchy(parent_layer, child_layer, transition_type="Portal")` - Create hierarchy between layers for transitions
    - `set_spatial_division_rules(layer_type, division_algorithm="Quadtree", max_depth=8, min_objects_per_cell=10)` - Set spatial division rules for layers
    - `configure_layer_streaming_manager(layer_type, streaming_policy="Distance", memory_budget_mb=512)` - Configure streaming manager for layers
    - `get_world_partition_status(level_name)` - Get current World Partition status of a level
    - `optimize_streaming_cells(level_name, optimization_type="Performance")` - Optimize streaming cells of a level
    
    ### World Partition Best Practices:
    
    **Layer Types:**
    - "Firmamento" - Sky/aerial layer for flying gameplay
    - "Planicie" - Ground/surface layer for standard gameplay
    - "Abismo" - Underground/depth layer for subterranean gameplay
    
    **Transition Types:**
    - "Portal" - Instant transition through portals
    - "Seamless" - Smooth transition without loading screens
    - "Teleport" - Instant teleportation between layers
    
    **Division Algorithms:**
    - "Quadtree" - 2D spatial division (recommended for most cases)
    - "Octree" - 3D spatial division (for complex vertical gameplay)
    - "Grid" - Simple grid-based division (for uniform content)
    
    **Streaming Policies:**
    - "Distance" - Load based on player distance
    - "Priority" - Load based on content priority
    - "Memory" - Load based on available memory
    
    **Optimization Types:**
    - "Performance" - Optimize for frame rate
    - "Memory" - Optimize for memory usage
    - "Quality" - Optimize for visual quality
    
    **Configuration Guidelines:**
    - Use appropriate grid_size based on world scale (25600 for large worlds)
    - Set cell_size to balance streaming performance (1600 recommended)
    - Configure streaming_distance based on gameplay needs
    - Use priority levels 1-10 for loading order
    - Monitor memory_budget_mb to prevent memory issues
    
    ## Realm Transition Tools
    - `create_realm_transition_system(realm_configs, transition_configs, global_settings=None)` - Create comprehensive realm transition systems
    - `create_transition_triggers(layer_name, trigger_configs)` - Create transition triggers between realms
    - `configure_realm_streaming(layer_name, streaming_configs)` - Configure realm streaming settings
    - `setup_realm_boundaries(layer_name, boundary_configs)` - Setup boundaries between realms
    - `optimize_transition_performance(layer_name, optimization_configs=None)` - Optimize transition performance
    - `debug_realm_transitions(layer_name, debug_level="info", debug_configs=None)` - Debug realm transition systems
    - `validate_realm_setup(layer_name, validation_rules)` - Validate realm configuration
    - `get_realm_system_status(layer_name=None)` - Get realm system status
    
    ### Realm Transition Best Practices:
    
    **Realm Configuration:**
    ```json
    {
      "realm_name": "sky_realm",
      "layer_type": "Firmamento",
      "streaming_distance": 10000.0,
      "transition_zones": [
        {
          "type": "Portal",
          "location": [0, 0, 5000],
          "target_realm": "ground_realm"
        }
      ]
    }
    ```
    
    **Transition Configuration:**
    ```json
    {
      "from_realm": "ground_realm",
      "to_realm": "sky_realm",
      "transition_type": "Portal",
      "trigger_distance": 500.0,
      "loading_screen": false,
      "fade_duration": 1.0
    }
    ```
    
    **Performance Guidelines:**
    - Use seamless transitions for nearby realms
    - Implement loading screens for distant realm transitions
    - Configure appropriate trigger distances to prevent pop-in
    - Monitor transition performance with debug tools
    - Validate realm setup before deployment
    
    ## Performance Optimization Tools
    - `create_performance_optimization_system(system_id, config=None)` - Create comprehensive performance optimization systems
    - `configure_dynamic_lod(system_id, target_objects=None, distance_thresholds=None, lod_levels=None, auto_calculate=True)` - Configure dynamic Level of Detail systems
    - `setup_culling_systems(system_id, culling_types=None, frustum_enabled=True, occlusion_enabled=True, distance_enabled=True, max_distance=10000.0)` - Setup advanced culling systems
    - `configure_memory_management(system_id, pool_sizes=None, auto_cleanup=True, gc_frequency=60.0, memory_budget_mb=2048)` - Configure memory management and garbage collection
    
    ## Vision System Tools
    - `create_fog_of_war_layer(layer_name, layer_height, fog_settings)` - Create fog of war layers for 3D vision systems
    - `configure_vision_range_layer(layer_name, vision_settings)` - Configure vision range settings for specific layers
    - `setup_line_of_sight_system(layer_name, los_settings)` - Setup line of sight systems for layers
    - `create_vision_blocking_volumes(layer_name, volumes_data)` - Create volumes that block vision in specific layers
    - `configure_dynamic_fog_updates(layer_name, update_settings)` - Configure dynamic fog of war updates
    - `configure_vision_occlusion_system(layer_name, occlusion_settings)` - Configure vision occlusion systems for optimization
    - `optimize_vision_performance(layer_name, optimization_settings)` - Optimize vision system performance
    - `debug_vision_system(layer_name, debug_options)` - Enable debug visualization for vision systems
    - `validate_vision_setup(layers_to_validate)` - Validate multilayer vision system configuration
    - `get_vision_system_status(include_performance_metrics=False)` - Get 3D vision system status and metrics
     
     ## Pathfinding System Tools
     - `create_navigation_mesh_layer(layer_name, layer_height=200.0, navmesh_settings=None)` - Create navigation mesh layers for 3D pathfinding
     - `configure_astar_algorithm(layer_name, algorithm_settings=None)` - Configure A* algorithm for specific layers
     - `set_movement_costs(layer_name, cost_settings)` - Set movement costs for terrain types and layer transitions
     - `create_layer_connections(source_layer, target_layer, connection_data)` - Create connections between navigation layers
     - `configure_pathfinding_constraints(constraints)` - Configure pathfinding constraints and optimizations
     - `create_dynamic_obstacles(obstacles)` - Create dynamic obstacles in the navigation system
     - `find_path_multilayer(start_position, end_position, agent_properties=None, allowed_layers=None)` - Find paths across multiple navigation layers
     - `optimize_navigation_performance(layer_name=None, optimization_settings=None)` - Optimize navigation performance for layers
     
     ## Procedural Generation System Tools
     - `create_procedural_generation_system(system_id, layers, generation_params, balance_config)` - Create comprehensive procedural generation systems
     - `generate_dynamic_objectives(player_context, objective_count=3, difficulty_target=2)` - Generate dynamic objectives based on player context
     - `configure_dynamic_balancing(balance_rules, monitoring_metrics, adjustment_thresholds, auto_adjust=True, adjustment_intensity=0.5, learning_rate=0.1)` - Configure dynamic balancing systems
     - `setup_content_generation(config)` - Setup procedural content generation
     - `configure_reward_scaling(config)` - Configure reward scaling systems
     - `optimize_generation_performance(config)` - Optimize generation performance
     - `debug_generation_system(debug_level="standard", system_id="default")` - Debug generation systems
     - `validate_generation_setup(config)` - Validate generation setup
     - `get_generation_system_status()` - Get generation system status
     
     ### Pathfinding System Best Practices:
     
     **Navigation Mesh Configuration:**
     - Use appropriate cell sizes for your game scale (default: 19.0 for human-scale)
     - Set agent properties to match your character dimensions
     - Configure max climb and slope values based on gameplay requirements
     - Use multiple layers for complex 3D environments (ground, platforms, air)
     
     **A* Algorithm Optimization:**
     - Choose appropriate heuristic types:
       - "euclidean" for open spaces with diagonal movement
       - "manhattan" for grid-based movement
       - "octile" for 8-directional movement
     - Adjust weight factor for performance vs accuracy trade-off
     - Set reasonable max search nodes to prevent infinite loops
     
     **Movement Cost Configuration:**
     ```json
     {
       "terrain_costs": {
         "grass": 1.0,
         "sand": 1.5,
         "water": 3.0,
         "mud": 2.0
       },
       "layer_transition_costs": {
         "ladder": 2.0,
         "stairs": 1.5,
         "elevator": 1.0
       },
       "default_cost": 1.0,
       "penalty_multiplier": 1.2
     }
     ```
     
     **Layer Connection Types:**
     - "ladder" - Vertical connections requiring climbing
     - "stairs" - Angled connections with moderate cost
     - "teleport" - Instant connections (portals, fast travel)
     - "jump" - Short-distance connections requiring jumping
     - "elevator" - Vertical connections with variable cost
     
     **Dynamic Obstacles:**
     - Use for temporary blockages (fallen trees, debris)
     - Configure appropriate sizes and costs
     - Remove obstacles when no longer needed to maintain performance
     - Consider using obstacle pooling for frequently changing environments
     
     **Performance Optimization:**
     - Enable tile generation optimization for large worlds
     - Use pathfinding cache for repeated queries
     - Set appropriate search timeouts to prevent frame drops
     - Configure memory usage limits based on available RAM
     - Use layer filtering to reduce search space
     
     **Multi-Layer Pathfinding:**
     - Define clear layer hierarchies (ground, platforms, air, underground)
     - Set up bidirectional connections where appropriate
     - Use agent properties to restrict access to certain layers
     - Configure arrival tolerance based on agent size and movement precision
     
     **Constraint Configuration Example:**
     ```json
     {
       "layer_name": "main_level",
       "max_search_nodes": 10000,
       "search_timeout_ms": 5000.0,
       "max_search_distance": 10000.0,
       "optimize_path": true,
       "smooth_path": true,
       "arrival_tolerance": 50.0
     }
     ```
     
     **Agent Properties Structure:**
     ```json
     {
       "radius": 34.0,
       "height": 180.0
     }
     ```
     
     ## Procedural Generation System Tools
     - `create_procedural_generation_system(system_id, layers, generation_params, balance_config)` - Create comprehensive procedural generation systems
     - `generate_dynamic_objectives(player_context, objective_count=3, difficulty_target=2)` - Generate dynamic objectives based on player context
     - `configure_dynamic_balancing(balance_rules, monitoring_metrics, adjustment_thresholds, auto_adjust=True, adjustment_intensity=0.5, learning_rate=0.1)` - Configure dynamic balancing systems
     - `setup_content_generation(config)` - Setup procedural content generation
     - `configure_reward_scaling(config)` - Configure reward scaling systems
     - `optimize_generation_performance(config)` - Optimize generation performance
     - `debug_generation_system(debug_level="standard", system_id="default")` - Debug generation systems
     - `validate_generation_setup(config)` - Validate generation setup
     - `get_generation_system_status()` - Get generation system status
     
     ### Procedural Generation Best Practices:
     
     **System Configuration:**
     - Define clear layer hierarchies for different content types (terrain, objects, quests, NPCs)
     - Configure generation parameters based on target performance and quality
     - Set up balanced difficulty progression systems
     - Use adaptive algorithms that respond to player behavior
     
     **Dynamic Objective Generation:**
     - Provide comprehensive player context including skill level, preferences, and progress
     - Set appropriate objective counts (3-7 for most scenarios)
     - Configure difficulty targets based on player progression (1-5 scale)
     - Use variety in objective types to maintain engagement
     
     **Dynamic Balancing Configuration:**
     ```json
     {
       "balance_rules": {
         "difficulty_scaling": {
           "min_difficulty": 1.0,
           "max_difficulty": 5.0,
           "scaling_factor": 0.1
         },
         "reward_scaling": {
           "base_multiplier": 1.0,
           "performance_bonus": 0.2,
           "difficulty_bonus": 0.15
         }
       },
       "monitoring_metrics": [
         "player_success_rate",
         "completion_time",
         "engagement_score",
         "difficulty_rating"
       ],
       "adjustment_thresholds": {
         "success_rate_min": 0.4,
         "success_rate_max": 0.8,
         "engagement_min": 0.6
       }
     }
     ```
     
     **Content Generation Types:**
     - "terrain" - Procedural landscape and environment generation
     - "structures" - Building and architectural generation
     - "quests" - Dynamic quest and mission creation
     - "npcs" - Non-player character generation with behaviors
     - "items" - Equipment and collectible generation
     - "events" - Dynamic event and encounter creation
     
     **Generation Parameters:**
     ```json
     {
       "seed": 12345,
       "complexity_level": "medium",
       "variation_factor": 0.7,
       "quality_target": "high",
       "performance_budget": {
         "max_generation_time_ms": 100,
         "memory_limit_mb": 256
       }
     }
     ```
     
     **Reward Scaling Configuration:**
     - Use "linear" scaling for predictable progression
     - Use "exponential" scaling for rapid advancement systems
     - Use "logarithmic" scaling for diminishing returns
     - Use "adaptive" scaling for player-responsive systems
     - Configure base rewards, multipliers, and bonus conditions
     
     **Performance Optimization:**
     - Set generation time limits to prevent frame drops
     - Use memory budgets to control resource usage
     - Enable caching for frequently generated content
     - Configure LOD systems for complex generated content
     - Monitor generation metrics and adjust parameters
     
     **Debugging and Validation:**
     - Use "minimal" debug level for production
     - Use "standard" debug level for development (recommended)
     - Use "detailed" debug level for troubleshooting
     - Use "verbose" debug level for deep analysis
     - Validate generation setup before deployment
     - Monitor system status regularly for performance issues
     
     **Layer Configuration Example:**
     ```json
     {
       "layer_id": "main_world",
       "layer_type": "terrain",
       "generation_rules": {
         "biome_distribution": {
           "forest": 0.3,
           "plains": 0.4,
           "mountains": 0.2,
           "water": 0.1
         },
         "feature_density": 0.6,
         "resource_distribution": "clustered"
       },
       "constraints": {
         "min_playable_area": 0.8,
         "max_slope_angle": 45.0,
         "accessibility_required": true
       }
     }
     ```
     
     ### Vision System Best Practices:
     
     **Fog of War Configuration:**
     - Use appropriate fog density values (0.0-1.0) for different gameplay needs
     - Configure reveal radius based on unit types and game balance
     - Set fade distance for smooth transitions between visible/hidden areas
     - Enable persistence for strategic gameplay elements
     
     **Line of Sight Setup:**
     - Choose appropriate trace precision ("low", "medium", "high") based on performance needs
     - Enable caching for frequently used line of sight calculations
     - Set reasonable max trace distances to avoid performance issues
     - Use proper trace channels for different vision types
     
     **Vision Blocking Volumes:**
     - Create volumes for terrain features, buildings, and other obstacles
     - Use appropriate volume shapes (box, sphere, custom) for different scenarios
     - Configure height-based blocking for multi-level environments
     - Set proper collision channels for vision blocking
     
     **Performance Optimization:**
     - Use LOD systems for distant vision calculations
     - Implement update frequency limits for non-critical vision updates
     - Enable spatial partitioning for large-scale environments
     - Use occlusion culling to reduce unnecessary calculations
     
     **Multi-Layer Vision Systems:**
     ```json
     {
       "layer_name": "ground_layer",
       "layer_height": 0.0,
       "fog_settings": {
         "density": 1.0,
         "update_frequency": 0.1,
         "reveal_radius": 500.0,
         "fade_distance": 100.0,
         "persistence_enabled": true
       },
       "vision_settings": {
         "max_range": 1000.0,
         "angle_of_view": 90.0,
         "height_tolerance": 50.0
       },
       "los_settings": {
         "trace_precision": "medium",
         "cache_enabled": true,
         "cache_duration": 1.0,
         "max_trace_distance": 10000.0,
         "trace_channel": "visibility"
       }
     }
     ```
    - `optimize_rendering_pipeline(system_id, enable_instancing=True, enable_batching=True, shadow_quality="medium", texture_streaming=True, max_draw_calls=1000)` - Optimize rendering pipeline performance
    - `monitor_performance_metrics(system_id, sample_interval=1.0, include_gpu_stats=True, include_memory_stats=True, include_render_stats=True)` - Monitor real-time performance metrics
    - `debug_performance_issues(system_id, enable_profiling=True, capture_callstack=False, analysis_depth="medium")` - Debug and analyze performance bottlenecks
    - `validate_performance_setup(system_id, check_compliance=True, performance_requirements=None)` - Validate performance optimization setup
    - `get_performance_system_status(system_id=None)` - Get current performance system status and metrics
     
     ### Performance Optimization Best Practices:
     
     **LOD (Level of Detail) Configuration:**
     - Use "ultra_high" for hero objects and close-up details
     - Use "high" for important gameplay objects
     - Use "medium" for standard environment objects
     - Use "low" for distant or background objects
     - Use "ultra_low" for very distant objects or performance-critical scenarios
     - Configure distance thresholds based on object importance and visual impact
     - Enable auto_calculate for automatic LOD distance optimization
     
     **Culling System Types:**
     - "frustum" - Remove objects outside camera view (always recommended)
     - "occlusion" - Remove objects blocked by other objects (performance intensive but effective)
     - "distance" - Remove objects beyond specified distance (good for open worlds)
     - "layer" - Layer-based culling for organized object management
     - "dynamic" - Adaptive culling based on performance metrics
     
     **Memory Management Strategies:**
     - Configure pool sizes based on content type and usage patterns
     - Enable auto_cleanup for automatic memory management
     - Set gc_frequency based on memory pressure (lower = more frequent cleanup)
     - Set memory_budget_mb based on target platform capabilities
     - Monitor memory pools: static_mesh, dynamic_mesh, texture, audio, animation, particle, ui, script
     
     **Rendering Pipeline Optimization:**
     - Enable instancing for repeated objects (trees, rocks, buildings)
     - Enable batching to reduce draw calls
     - Configure shadow_quality: "low", "medium", "high", "ultra" based on performance needs
     - Enable texture_streaming for large worlds with many textures
     - Set max_draw_calls based on target platform (mobile: 500-1000, PC: 1000-3000, console: 2000-5000)
     
     **Performance Monitoring Guidelines:**
     - Use sample_interval of 0.5-2.0 seconds for real-time monitoring
     - Enable GPU stats for graphics-intensive applications
     - Enable memory stats for memory-constrained platforms
     - Enable render stats for draw call and rendering optimization
     - Monitor continuously during development and testing
     
     **Performance Debugging Workflow:**
     - Enable profiling during performance issues
     - Capture callstack only when investigating specific bottlenecks (performance cost)
     - Use analysis_depth: "shallow" for quick checks, "medium" for standard analysis, "deep" for thorough investigation
     - Focus on frame time, draw calls, memory usage, and GPU utilization
     
     **Performance Validation:**
     - Set check_compliance=true to validate against performance standards
     - Define performance_requirements based on target platform:
       - Mobile: 30-60 FPS, <2GB RAM, <500 draw calls
       - Console: 30-60 FPS, <8GB RAM, <2000 draw calls
       - PC: 60+ FPS, <16GB RAM, <3000 draw calls
     - Validate regularly during development cycles
     
     **System Configuration Example:**
     ```json
     {
       "system_id": "main_performance_system",
       "config": {
         "scalability_settings": {
           "view_distance_scale": 1.0,
           "shadow_distance_scale": 0.8,
           "texture_quality": "high",
           "effects_quality": "medium"
         },
         "tick_optimization": {
           "enable_tick_pooling": true,
           "max_tick_rate": 60.0,
           "priority_system": true
         }
       }
     }
     ```
     
     ### Network Tools Best Practices:
    
    **Replication Modes:**
    - Use "reliable" for critical gameplay data (health, score, state changes)
    - Use "unreliable" for frequent updates (position, rotation)
    - Use "reliable_ordered" for sequential important events
    - Use "unreliable_sequenced" for rapid state updates
    
    **Network Roles:**
    - "authority" - Server authoritative objects
    - "autonomous_proxy" - Player-controlled objects with prediction
    - "simulated_proxy" - AI or other players' objects
    - "none" - Local-only objects
    
    **Prediction Types:**
    - "movement" - Character/vehicle movement prediction
    - "physics" - Physics object prediction
    - "animation" - Animation state prediction
    - "gameplay" - Game logic prediction
    - "custom" - Custom prediction logic
    
    **Performance Guidelines:**
    - Keep tick rates between 20-60Hz for most gameplay
    - Use relevancy distance to limit replication scope
    - Enable compression for large data transfers
    - Monitor bandwidth usage per layer (target <100KB/s per player)
    - Use prediction for responsive client experience
    - Implement lag compensation for hit detection
    
    **Layer Configuration Example:**
    ```json
    {
      "layer_name": "gameplay_layer",
      "max_players": 32,
      "tick_rate": 30.0,
      "bandwidth_limit_kbps": 100,
      "compression_enabled": true,
      "encryption_enabled": false,
      "priority": 1
    }
    ```
    
    ### AI Tools Best Practices:
    
    **Learning Pipeline Configuration:**
    - Use `"reinforcement"` learning for adaptive NPCs that learn from player behavior
    - Use `"supervised"` learning for predictable AI patterns with training data
    - Use `"unsupervised"` learning for emergent behavior discovery
    - Configure `model_config` with appropriate parameters for your use case
    - Provide `training_data_path` for supervised learning scenarios
    
    **Adaptive Behavior Setup:**
    - Set `response_threshold` between 0.5-0.9 for sensitivity control
    - Use `"aggressive"` behavior for combat-focused NPCs
    - Use `"defensive"` behavior for protective or cautious NPCs
    - Use `"neutral"` behavior for balanced interactions
    - Use `"adaptive"` behavior for dynamic response to player actions
    - Define `adaptation_rules` with conditions like player proximity, health status, etc.
    
    **Decision Tree Configuration:**
    - Create meaningful `tree_name` for easy identification
    - Define `decision_nodes` with clear conditions and actions
    - Enable `learning_enabled` for trees that should adapt over time
    - Structure nodes hierarchically for complex decision making
    
    **Memory System Setup:**
    - Use `"short_term"` memory for immediate tactical decisions
    - Use `"long_term"` memory for persistent player behavior patterns
    - Use `"episodic"` memory for specific event recall
    - Set `memory_capacity` based on AI complexity (50-200 typical)
    - Configure `retention_time` in seconds (60-600 for most scenarios)
    
    **Performance Optimization:**
    - Use `"low"` optimization for simple AI with minimal processing
    - Use `"balanced"` optimization for standard gameplay AI (recommended)
    - Use `"high"` optimization for complex AI with advanced features
    - Use `"extreme"` optimization only for performance-critical scenarios
    - Monitor `performance_metrics` like CPU usage, memory consumption, response time
    
    **Debugging and Validation:**
    - Use `"error"` debug level for critical issues only
    - Use `"warning"` debug level for potential problems
    - Use `"info"` debug level for general system information (recommended)
    - Use `"verbose"` debug level for detailed debugging
    - Specify `debug_components` to focus on specific AI subsystems
    - Run `validate_ai_setup` with specific `validation_tests` for targeted checks
    
    **AI Layer Configuration Example:**
    ```json
    {
      "layer_name": "npc_combat_layer",
      "learning_type": "reinforcement",
      "behavior_type": "adaptive",
      "memory_type": "short_term",
      "memory_capacity": 100,
      "retention_time": 300.0,
      "optimization_level": "balanced",
      "response_threshold": 0.7
    }
    ```
    - `configure_collision_complexity(actor_name, complexity_type, custom_settings)` - Configure actor collision complexity
    - `setup_layer_collision_filtering(layer_name, filter_settings)` - Setup collision filtering for layers
    
    ## Best Practices
    
    ### Multi-Layer Collision System
    - Create custom collision channels for different game layers (Player, Environment, Projectiles, etc.)
    - Use descriptive names for collision channels and profiles
    - Configure collision profiles to define how objects interact with each channel
    - Set up layer-specific collision rules to control inter-layer interactions
    - Use collision size scaling to optimize performance for different object sizes
    - Create collision interaction matrices to define complex layer relationships
    - Optimize collision detection settings based on layer importance and frequency
    - Monitor collision system status regularly for performance issues
    - Configure appropriate collision complexity (Simple, Complex, Use Complex as Simple)
    - Implement collision filtering to reduce unnecessary collision checks
    
    ### UMG Widget Development
    - Create widgets with descriptive names that reflect their purpose
    - Use consistent naming conventions for widget components
    - Organize widget hierarchy logically
    - Set appropriate anchors and alignment for responsive layouts
    - Use property bindings for dynamic updates instead of direct setting
    - Handle widget events appropriately with meaningful function names
    - Clean up widgets when no longer needed
    - Test widget layouts at different resolutions
    
    ### Editor and Actor Management
    - Use unique names for actors to avoid conflicts
    - Clean up temporary actors
    - Validate transforms before applying
    - Check actor existence before modifications
    - Take regular viewport screenshots during development
    - Keep the viewport focused on relevant actors during operations
    
    ### Blueprint Development
    - Compile Blueprints after changes
    - Use meaningful names for variables and functions
    - Organize nodes logically
    - Test functionality in isolation
    - Consider performance implications
    - Document complex setups
    
    ### Error Handling
    - Check command responses for success
    - Handle errors gracefully
    - Log important operations
    - Validate parameters
    - Clean up resources on errors
    """

# Run the server
if __name__ == "__main__":
    logger.info("Starting MCP server with stdio transport")
    mcp.run(transport='stdio')