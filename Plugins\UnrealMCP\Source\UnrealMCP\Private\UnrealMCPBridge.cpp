#include "UnrealMCPBridge.h"
#include "MCPServerRunnable.h"
#include "Commands/UnrealMCPWorldPartitionCommands.h"
#include "Commands/UnrealMCPCollisionCommands.h"
#include "Commands/UnrealMCPPathfindingCommands.h"
#include "Commands/UnrealMCPVisionCommands.h"
#include "Commands/UnrealMCPAICommands.h"
#include "Commands/UnrealMCPRealmCommands.h"
#include "Commands/UnrealMCPNetworkCommands.h"
#include "Commands/UnrealMCPProceduralCommands.h"
#include "Commands/UnrealMCPPerformanceCommands.h"
#include "Commands/UnrealMCPMaterialCommands.h"
#include "Commands/UnrealMCPNiagaraCommands.h"
#include "Commands/UnrealMCPChaosPhysicsCommands.h"
#include "Commands/UnrealMCPRenderingPipelineCommands.h"
#include "Commands/UnrealMCPHardwareDetectionCommands.h"
#include "Commands/UnrealMCPAudioSystemCommands.h"
#include "Commands/UnrealMCPNetworkingCommands.h"
#include "Commands/UnrealMCPPlatformCommands.h"
#include "Sockets.h"
#include "SocketSubsystem.h"
#include "HAL/RunnableThread.h"
#include "Interfaces/IPv4/IPv4Address.h"
#include "Interfaces/IPv4/IPv4Endpoint.h"
#include "Dom/JsonObject.h"
#include "Dom/JsonValue.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonReader.h"
#include "Serialization/JsonWriter.h"
#include "Engine/StaticMeshActor.h"
#include "Engine/DirectionalLight.h"
#include "Engine/PointLight.h"
#include "Engine/SpotLight.h"
#include "Camera/CameraActor.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "JsonObjectConverter.h"
#include "GameFramework/Actor.h"
#include "Engine/Selection.h"
#include "Kismet/GameplayStatics.h"
#include "Async/Async.h"
// Add Blueprint related includes
#include "Engine/Blueprint.h"
#include "Engine/BlueprintGeneratedClass.h"
#include "Factories/BlueprintFactory.h"
#include "EdGraphSchema_K2.h"
#include "K2Node_Event.h"
#include "K2Node_VariableGet.h"
#include "K2Node_VariableSet.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Components/SphereComponent.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "Kismet2/KismetEditorUtilities.h"
// UE5.5 correct includes
#include "Engine/SimpleConstructionScript.h"
#include "Engine/SCS_Node.h"
#include "UObject/Field.h"
#include "UObject/FieldPath.h"
// Blueprint Graph specific includes
#include "EdGraph/EdGraph.h"
#include "EdGraph/EdGraphNode.h"
#include "EdGraph/EdGraphPin.h"
#include "K2Node_CallFunction.h"
#include "K2Node_InputAction.h"
#include "K2Node_Self.h"
#include "GameFramework/InputSettings.h"
#include "EditorSubsystem.h"
#include "Subsystems/EditorActorSubsystem.h"
// Include our new command handler classes
#include "Commands/UnrealMCPEditorCommands.h"
#include "Commands/UnrealMCPBlueprintCommands.h"
#include "Commands/UnrealMCPBlueprintNodeCommands.h"
#include "Commands/UnrealMCPProjectCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"
#include "Commands/UnrealMCPUMGCommands.h"

// Default settings
#define MCP_SERVER_HOST "127.0.0.1"
#define MCP_SERVER_PORT 55557

UUnrealMCPBridge::UUnrealMCPBridge()
{
    EditorCommands = MakeShared<FUnrealMCPEditorCommands>();
    BlueprintCommands = MakeShared<FUnrealMCPBlueprintCommands>();
    BlueprintNodeCommands = MakeShared<FUnrealMCPBlueprintNodeCommands>();
    ProjectCommands = MakeShared<FUnrealMCPProjectCommands>();
    UMGCommands = MakeShared<FUnrealMCPUMGCommands>();
    WorldPartitionCommands = MakeShared<FUnrealMCPWorldPartitionCommands>();
    CollisionCommands = MakeShared<FUnrealMCPCollisionCommands>();
    PathfindingCommands = MakeShared<FUnrealMCPPathfindingCommands>();
    VisionCommands = MakeShared<FUnrealMCPVisionCommands>();
    AICommands = MakeShared<FUnrealMCPAICommands>();
    RealmCommands = MakeShared<FUnrealMCPRealmCommands>();
    NetworkCommands = MakeShared<FUnrealMCPNetworkCommands>();
    ProceduralCommands = MakeShared<FUnrealMCPProceduralCommands>();
    PerformanceCommands = MakeShared<FUnrealMCPPerformanceCommands>();
}

UUnrealMCPBridge::~UUnrealMCPBridge()
{
    EditorCommands.Reset();
    BlueprintCommands.Reset();
    BlueprintNodeCommands.Reset();
    ProjectCommands.Reset();
    UMGCommands.Reset();
    WorldPartitionCommands.Reset();
    CollisionCommands.Reset();
    PathfindingCommands.Reset();
    VisionCommands.Reset();
    AICommands.Reset();
    RealmCommands.Reset();
    NetworkCommands.Reset();
    ProceduralCommands.Reset();
    PerformanceCommands.Reset();
}

// Initialize subsystem
void UUnrealMCPBridge::Initialize(FSubsystemCollectionBase& Collection)
{
    UE_LOG(LogTemp, Display, TEXT("UnrealMCPBridge: Initializing"));
    
    bIsRunning = false;
    ListenerSocket = nullptr;
    ConnectionSocket = nullptr;
    ServerThread = nullptr;
    Port = MCP_SERVER_PORT;
    FIPv4Address::Parse(MCP_SERVER_HOST, ServerAddress);

    // Start the server automatically
    StartServer();
}

// Clean up resources when subsystem is destroyed
void UUnrealMCPBridge::Deinitialize()
{
    UE_LOG(LogTemp, Display, TEXT("UnrealMCPBridge: Shutting down"));
    StopServer();
}

// Start the MCP server
void UUnrealMCPBridge::StartServer()
{
    if (bIsRunning)
    {
        UE_LOG(LogTemp, Warning, TEXT("UnrealMCPBridge: Server is already running"));
        return;
    }

    // Create socket subsystem
    ISocketSubsystem* SocketSubsystem = ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM);
    if (!SocketSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("UnrealMCPBridge: Failed to get socket subsystem"));
        return;
    }

    // Create listener socket
    TSharedPtr<FSocket> NewListenerSocket = MakeShareable(SocketSubsystem->CreateSocket(NAME_Stream, TEXT("UnrealMCPListener"), false));
    if (!NewListenerSocket.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("UnrealMCPBridge: Failed to create listener socket"));
        return;
    }

    // Allow address reuse for quick restarts
    NewListenerSocket->SetReuseAddr(true);
    NewListenerSocket->SetNonBlocking(true);

    // Bind to address
    FIPv4Endpoint Endpoint(ServerAddress, Port);
    if (!NewListenerSocket->Bind(*Endpoint.ToInternetAddr()))
    {
        UE_LOG(LogTemp, Error, TEXT("UnrealMCPBridge: Failed to bind listener socket to %s:%d"), *ServerAddress.ToString(), Port);
        return;
    }

    // Start listening
    if (!NewListenerSocket->Listen(5))
    {
        UE_LOG(LogTemp, Error, TEXT("UnrealMCPBridge: Failed to start listening"));
        return;
    }

    ListenerSocket = NewListenerSocket;
    bIsRunning = true;
    UE_LOG(LogTemp, Display, TEXT("UnrealMCPBridge: Server started on %s:%d"), *ServerAddress.ToString(), Port);

    // Start server thread
    ServerThread = FRunnableThread::Create(
        new FMCPServerRunnable(this, ListenerSocket),
        TEXT("UnrealMCPServerThread"),
        0, TPri_Normal
    );

    if (!ServerThread)
    {
        UE_LOG(LogTemp, Error, TEXT("UnrealMCPBridge: Failed to create server thread"));
        StopServer();
        return;
    }
}

// Stop the MCP server
void UUnrealMCPBridge::StopServer()
{
    if (!bIsRunning)
    {
        return;
    }

    bIsRunning = false;

    // Clean up thread
    if (ServerThread)
    {
        ServerThread->Kill(true);
        delete ServerThread;
        ServerThread = nullptr;
    }

    // Close sockets
    if (ConnectionSocket.IsValid())
    {
        ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM)->DestroySocket(ConnectionSocket.Get());
        ConnectionSocket.Reset();
    }

    if (ListenerSocket.IsValid())
    {
        ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM)->DestroySocket(ListenerSocket.Get());
        ListenerSocket.Reset();
    }

    UE_LOG(LogTemp, Display, TEXT("UnrealMCPBridge: Server stopped"));
}

// Execute a command received from a client
FString UUnrealMCPBridge::ExecuteCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params)
{
    UE_LOG(LogTemp, Display, TEXT("UnrealMCPBridge: Executing command: %s"), *CommandType);
    
    // Create a promise to wait for the result
    TPromise<FString> Promise;
    TFuture<FString> Future = Promise.GetFuture();
    
    // Queue execution on Game Thread
    AsyncTask(ENamedThreads::GameThread, [this, CommandType, Params, Promise = MoveTemp(Promise)]() mutable
    {
        TSharedPtr<FJsonObject> ResponseJson = MakeShareable(new FJsonObject);
        
        try
        {
            TSharedPtr<FJsonObject> ResultJson;
            
            if (CommandType == TEXT("ping"))
            {
                ResultJson = MakeShareable(new FJsonObject);
                ResultJson->SetStringField(TEXT("message"), TEXT("pong"));
            }
            // Editor Commands (including actor manipulation)
            else if (CommandType == TEXT("get_actors_in_level") || 
                     CommandType == TEXT("find_actors_by_name") ||
                     CommandType == TEXT("spawn_actor") ||
                     CommandType == TEXT("create_actor") ||
                     CommandType == TEXT("delete_actor") || 
                     CommandType == TEXT("set_actor_transform") ||
                     CommandType == TEXT("get_actor_properties") ||
                     CommandType == TEXT("set_actor_property") ||
                     CommandType == TEXT("spawn_blueprint_actor") ||
                     CommandType == TEXT("focus_viewport") || 
                     CommandType == TEXT("take_screenshot"))
            {
                ResultJson = EditorCommands->HandleCommand(CommandType, Params);
            }
            // Blueprint Commands
            else if (CommandType == TEXT("create_blueprint") || 
                     CommandType == TEXT("add_component_to_blueprint") || 
                     CommandType == TEXT("set_component_property") || 
                     CommandType == TEXT("set_physics_properties") || 
                     CommandType == TEXT("compile_blueprint") || 
                     CommandType == TEXT("set_blueprint_property") || 
                     CommandType == TEXT("set_static_mesh_properties") ||
                     CommandType == TEXT("set_pawn_properties"))
            {
                ResultJson = BlueprintCommands->HandleCommand(CommandType, Params);
            }
            // Blueprint Node Commands
            else if (CommandType == TEXT("connect_blueprint_nodes") || 
                     CommandType == TEXT("add_blueprint_get_self_component_reference") ||
                     CommandType == TEXT("add_blueprint_self_reference") ||
                     CommandType == TEXT("find_blueprint_nodes") ||
                     CommandType == TEXT("add_blueprint_event_node") ||
                     CommandType == TEXT("add_blueprint_input_action_node") ||
                     CommandType == TEXT("add_blueprint_function_node") ||
                     CommandType == TEXT("add_blueprint_get_component_node") ||
                     CommandType == TEXT("add_blueprint_variable"))
            {
                ResultJson = BlueprintNodeCommands->HandleCommand(CommandType, Params);
            }
            // Project Commands
            else if (CommandType == TEXT("create_input_mapping"))
            {
                ResultJson = ProjectCommands->HandleCommand(CommandType, Params);
            }
            // UMG Commands
            else if (CommandType == TEXT("create_umg_widget_blueprint") ||
                     CommandType == TEXT("add_text_block_to_widget") ||
                     CommandType == TEXT("add_button_to_widget") ||
                     CommandType == TEXT("bind_widget_event") ||
                     CommandType == TEXT("set_text_block_binding") ||
                     CommandType == TEXT("add_widget_to_viewport"))
            {
                ResultJson = UMGCommands->HandleCommand(CommandType, Params);
            }
            // World Partition Commands
            else if (CommandType == TEXT("create_world_partition_level") ||
                     CommandType == TEXT("configure_streaming_cell") ||
                     CommandType == TEXT("create_layer_hierarchy") ||
                     CommandType == TEXT("set_spatial_division_rules") ||
                     CommandType == TEXT("configure_layer_streaming_manager") ||
                     CommandType == TEXT("get_world_partition_status") ||
                     CommandType == TEXT("optimize_streaming_cells"))
            {
                ResultJson = WorldPartitionCommands->HandleCommand(CommandType, Params);
            }
            // Collision Commands
            else if (CommandType == TEXT("create_collision_channel") ||
                     CommandType == TEXT("configure_collision_profile") ||
                     CommandType == TEXT("set_layer_collision_rules") ||
                     CommandType == TEXT("configure_collision_size_scaling") ||
                     CommandType == TEXT("create_layer_collision_matrix") ||
                     CommandType == TEXT("optimize_collision_detection") ||
                     CommandType == TEXT("configure_collision_complexity") ||
                     CommandType == TEXT("setup_layer_collision_filtering") ||
                     CommandType == TEXT("get_collision_system_status") ||
                     // Auracron-specific Collision Commands
                     CommandType == TEXT("setup_auracron_collision_channels") ||
                     CommandType == TEXT("configure_dota2_collision_sizes") ||
                     CommandType == TEXT("setup_layer_collision_interactions") ||
                     CommandType == TEXT("configure_auracron_collision_profiles"))
            {
                ResultJson = CollisionCommands->HandleCommand(CommandType, Params);
            }
            // Pathfinding Commands
            else if (CommandType == TEXT("create_navigation_mesh_layer") ||
                     CommandType == TEXT("configure_astar_algorithm") ||
                     CommandType == TEXT("set_movement_costs") ||
                     CommandType == TEXT("create_layer_connections") ||
                     CommandType == TEXT("configure_pathfinding_constraints") ||
                     CommandType == TEXT("create_dynamic_obstacles") ||
                     CommandType == TEXT("find_path_multilayer") ||
                     CommandType == TEXT("optimize_navigation_performance") ||
                     CommandType == TEXT("configure_hierarchical_pathfinding") ||
                     CommandType == TEXT("setup_crowd_navigation") ||
                     CommandType == TEXT("debug_navigation_layer") ||
                     CommandType == TEXT("validate_navigation_setup") ||
                     CommandType == TEXT("get_pathfinding_system_status"))
            {
                ResultJson = PathfindingCommands->HandleCommand(CommandType, Params);
            }
            // Vision Commands
            else if (CommandType == TEXT("create_fog_of_war_layer") ||
                     CommandType == TEXT("configure_vision_range_layer") ||
                     CommandType == TEXT("setup_line_of_sight_system") ||
                     CommandType == TEXT("create_vision_blocking_volumes") ||
                     CommandType == TEXT("configure_dynamic_fog_updates") ||
                     CommandType == TEXT("setup_multilayer_vision_interactions") ||
                     CommandType == TEXT("create_vision_sensors") ||
                     CommandType == TEXT("configure_vision_occlusion_system") ||
                     CommandType == TEXT("setup_fog_of_war_persistence") ||
                     CommandType == TEXT("calculate_vision_coverage") ||
                     CommandType == TEXT("optimize_vision_performance") ||
                     CommandType == TEXT("debug_vision_system") ||
                     CommandType == TEXT("validate_vision_setup") ||
                     CommandType == TEXT("get_vision_system_status") ||
                     // Auracron-specific Vision Commands
                     CommandType == TEXT("configure_auracron_vision_layers") ||
                     CommandType == TEXT("setup_multilayer_vision_system") ||
                     CommandType == TEXT("configure_layer_vision_ranges") ||
                     CommandType == TEXT("setup_vertical_connector_vision"))
            {
                ResultJson = VisionCommands->HandleCommand(CommandType, Params);
            }
            // AI Commands
            else if (CommandType == TEXT("create_ai_learning_pipeline") ||
                     CommandType == TEXT("configure_adaptive_behavior") ||
                     CommandType == TEXT("setup_dynamic_spawn_system") ||
                     CommandType == TEXT("create_ai_decision_tree") ||
                     CommandType == TEXT("configure_special_events") ||
                     CommandType == TEXT("setup_ai_communication_system") ||
                     CommandType == TEXT("configure_player_profiling") ||
                     CommandType == TEXT("setup_ai_memory_system") ||
                     CommandType == TEXT("optimize_ai_performance") ||
                     CommandType == TEXT("debug_ai_system") ||
                     CommandType == TEXT("validate_ai_setup") ||
                     CommandType == TEXT("get_ai_system_status"))
            {
                ResultJson = AICommands->HandleCommand(CommandType, Params);
            }
            // Realm Commands
            else if (CommandType == TEXT("create_realm_transition_system") ||
                     CommandType == TEXT("configure_asset_streaming") ||
                     CommandType == TEXT("setup_world_partitioning") ||
                     CommandType == TEXT("create_transition_triggers") ||
                     CommandType == TEXT("configure_realm_persistence") ||
                     CommandType == TEXT("setup_cross_realm_communication") ||
                     CommandType == TEXT("optimize_transition_performance") ||
                     CommandType == TEXT("debug_realm_transitions") ||
                     CommandType == TEXT("validate_realm_setup") ||
                     CommandType == TEXT("get_realm_system_status") ||
                     // Analytics and Telemetry Commands
                     CommandType == TEXT("start_analytics_collection") ||
                     CommandType == TEXT("stop_analytics_collection") ||
                     CommandType == TEXT("collect_gameplay_metrics") ||
                     CommandType == TEXT("process_analytics_data") ||
                     CommandType == TEXT("generate_analytics_visualization") ||
                     CommandType == TEXT("configure_metric_alerts") ||
                     CommandType == TEXT("export_analytics_data") ||
                     CommandType == TEXT("import_analytics_data") ||
                     CommandType == TEXT("get_performance_statistics") ||
                     CommandType == TEXT("setup_custom_telemetry") ||
                     CommandType == TEXT("generate_player_behavior_report") ||
                     CommandType == TEXT("create_realtime_dashboard") ||
                     CommandType == TEXT("aggregate_multilayer_data") ||
                     CommandType == TEXT("run_predictive_analysis") ||
                     CommandType == TEXT("setup_ab_testing") ||
                     CommandType == TEXT("generate_heatmap_analysis"))
            {
                ResultJson = RealmCommands->HandleCommand(CommandType, Params);
            }
            // Network Commands
            else if (CommandType == TEXT("create_multilayer_network_system") ||
                     CommandType == TEXT("configure_object_replication") ||
                     CommandType == TEXT("setup_client_prediction") ||
                     CommandType == TEXT("configure_network_synchronization") ||
                     CommandType == TEXT("setup_lag_compensation") ||
                     CommandType == TEXT("configure_bandwidth_optimization") ||
                     CommandType == TEXT("debug_network_performance") ||
                     CommandType == TEXT("validate_network_setup") ||
                     CommandType == TEXT("get_network_system_status"))
            {
                ResultJson = NetworkCommands->HandleCommand(CommandType, Params);
            }
            // Procedural Commands
            else if (CommandType == TEXT("create_procedural_generation_system") ||
                     CommandType == TEXT("generate_dynamic_objectives") ||
                     CommandType == TEXT("configure_dynamic_balancing") ||
                     CommandType == TEXT("setup_content_generation") ||
                     CommandType == TEXT("configure_reward_scaling") ||
                     CommandType == TEXT("optimize_generation_performance") ||
                     CommandType == TEXT("debug_generation_system") ||
                     CommandType == TEXT("validate_generation_setup") ||
                     CommandType == TEXT("get_generation_system_status"))
            {
                ResultJson = ProceduralCommands->HandleCommand(CommandType, Params);
            }
            // Performance Commands
            else if (CommandType == TEXT("create_performance_optimization_system") ||
                     CommandType == TEXT("configure_dynamic_lod") ||
                     CommandType == TEXT("setup_culling_systems") ||
                     CommandType == TEXT("configure_memory_management") ||
                     CommandType == TEXT("optimize_rendering_pipeline") ||
                     CommandType == TEXT("monitor_performance_metrics") ||
                     CommandType == TEXT("debug_performance_issues") ||
                     CommandType == TEXT("validate_performance_setup") ||
                     CommandType == TEXT("get_performance_system_status"))
            {
                ResultJson = PerformanceCommands->HandleCommand(CommandType, Params);
            }
            // Material Commands
            else if (CommandType == TEXT("create_material") ||
                     CommandType == TEXT("create_material_instance") ||
                     CommandType == TEXT("add_material_expression") ||
                     CommandType == TEXT("connect_material_expressions") ||
                     CommandType == TEXT("set_material_parameter") ||
                     CommandType == TEXT("compile_material") ||
                     CommandType == TEXT("configure_material_for_hardware_tier") ||
                     CommandType == TEXT("create_procedural_material") ||
                     CommandType == TEXT("create_material_function") ||
                     CommandType == TEXT("create_layered_material") ||
                     CommandType == TEXT("analyze_material_performance"))
            {
                // Convert JSON object to string for material commands
                FString JsonString;
                TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
                FJsonSerializer::Serialize(Params.ToSharedRef(), Writer);
                
                FString Response;
                if (CommandType == TEXT("create_material"))
                {
                    Response = UnrealMCPMaterialCommands::HandleCreateMaterial(JsonString);
                }
                else if (CommandType == TEXT("create_material_instance"))
                {
                    Response = UnrealMCPMaterialCommands::HandleCreateMaterialInstance(JsonString);
                }
                else if (CommandType == TEXT("add_material_expression"))
                {
                    Response = UnrealMCPMaterialCommands::HandleAddMaterialExpression(JsonString);
                }
                else if (CommandType == TEXT("connect_material_expressions"))
                {
                    Response = UnrealMCPMaterialCommands::HandleConnectMaterialExpressions(JsonString);
                }
                else if (CommandType == TEXT("set_material_parameter"))
                {
                    Response = UnrealMCPMaterialCommands::HandleSetMaterialParameter(JsonString);
                }
                else if (CommandType == TEXT("compile_material"))
                {
                    Response = UnrealMCPMaterialCommands::HandleCompileMaterial(JsonString);
                }
                else if (CommandType == TEXT("configure_material_for_hardware_tier"))
                {
                    Response = UnrealMCPMaterialCommands::HandleConfigureMaterialForHardwareTier(JsonString);
                }
                else if (CommandType == TEXT("create_procedural_material"))
                {
                    Response = UnrealMCPMaterialCommands::HandleCreateProceduralMaterial(JsonString);
                }
                else if (CommandType == TEXT("create_material_function"))
                {
                    Response = UnrealMCPMaterialCommands::HandleCreateMaterialFunction(JsonString);
                }
                else if (CommandType == TEXT("create_layered_material"))
                {
                    Response = UnrealMCPMaterialCommands::HandleCreateLayeredMaterial(JsonString);
                }
                else if (CommandType == TEXT("analyze_material_performance"))
                {
                    Response = UnrealMCPMaterialCommands::HandleAnalyzeMaterialPerformance(JsonString);
                }

                // Parse the response JSON
                TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response);
                if (!FJsonSerializer::Deserialize(Reader, ResultJson) || !ResultJson.IsValid())
                {
                    ResultJson = MakeShareable(new FJsonObject);
                    ResultJson->SetBoolField(TEXT("success"), false);
                    ResultJson->SetStringField(TEXT("message"), TEXT("Failed to parse material command response"));
                }
            }
            // Niagara Commands
            else if (CommandType == TEXT("create_niagara_system") ||
                     CommandType == TEXT("create_niagara_emitter") ||
                     CommandType == TEXT("add_emitter_to_system") ||
                     CommandType == TEXT("add_niagara_module") ||
                     CommandType == TEXT("set_niagara_parameter") ||
                     CommandType == TEXT("spawn_niagara_system_at_location") ||
                     CommandType == TEXT("create_particle_light_effect") ||
                     CommandType == TEXT("create_gpu_sprite_effect") ||
                     CommandType == TEXT("create_ribbon_effect") ||
                     CommandType == TEXT("create_mesh_particle_effect") ||
                     CommandType == TEXT("create_beam_effect") ||
                     CommandType == TEXT("optimize_niagara_performance"))
            {
                // Convert JSON object to string for Niagara commands
                FString JsonString;
                TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
                FJsonSerializer::Serialize(Params.ToSharedRef(), Writer);

                FString Response;
                if (CommandType == TEXT("create_niagara_system"))
                {
                    Response = UnrealMCPNiagaraCommands::HandleCreateNiagaraSystem(JsonString);
                }
                else if (CommandType == TEXT("create_niagara_emitter"))
                {
                    Response = UnrealMCPNiagaraCommands::HandleCreateNiagaraEmitter(JsonString);
                }
                else if (CommandType == TEXT("add_emitter_to_system"))
                {
                    Response = UnrealMCPNiagaraCommands::HandleAddEmitterToSystem(JsonString);
                }
                else if (CommandType == TEXT("add_niagara_module"))
                {
                    Response = UnrealMCPNiagaraCommands::HandleAddNiagaraModule(JsonString);
                }
                else if (CommandType == TEXT("set_niagara_parameter"))
                {
                    Response = UnrealMCPNiagaraCommands::HandleSetNiagaraParameter(JsonString);
                }
                else if (CommandType == TEXT("spawn_niagara_system_at_location"))
                {
                    Response = UnrealMCPNiagaraCommands::HandleSpawnNiagaraSystemAtLocation(JsonString);
                }
                else if (CommandType == TEXT("create_particle_light_effect"))
                {
                    Response = UnrealMCPNiagaraCommands::HandleCreateParticleLightEffect(JsonString);
                }
                else if (CommandType == TEXT("create_gpu_sprite_effect"))
                {
                    Response = UnrealMCPNiagaraCommands::HandleCreateGPUSpriteEffect(JsonString);
                }
                else if (CommandType == TEXT("create_ribbon_effect"))
                {
                    Response = UnrealMCPNiagaraCommands::HandleCreateRibbonEffect(JsonString);
                }
                else if (CommandType == TEXT("create_mesh_particle_effect"))
                {
                    Response = UnrealMCPNiagaraCommands::HandleCreateMeshParticleEffect(JsonString);
                }
                else if (CommandType == TEXT("create_beam_effect"))
                {
                    Response = UnrealMCPNiagaraCommands::HandleCreateBeamEffect(JsonString);
                }
                else if (CommandType == TEXT("optimize_niagara_performance"))
                {
                    Response = UnrealMCPNiagaraCommands::HandleOptimizeNiagaraPerformance(JsonString);
                }

                // Parse the response JSON
                TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response);
                if (!FJsonSerializer::Deserialize(Reader, ResultJson) || !ResultJson.IsValid())
                {
                    ResultJson = MakeShareable(new FJsonObject);
                    ResultJson->SetBoolField(TEXT("success"), false);
                    ResultJson->SetStringField(TEXT("message"), TEXT("Failed to parse Niagara command response"));
                }
            }
            // Chaos Physics Commands
            else if (CommandType == TEXT("create_geometry_collection") ||
                     CommandType == TEXT("fracture_geometry_collection") ||
                     CommandType == TEXT("create_chaos_physics_field") ||
                     CommandType == TEXT("spawn_destructible_actor") ||
                     CommandType == TEXT("apply_chaos_damage") ||
                     CommandType == TEXT("create_fluid_simulation") ||
                     CommandType == TEXT("configure_chaos_solver") ||
                     CommandType == TEXT("create_cloth_simulation") ||
                     CommandType == TEXT("optimize_chaos_performance") ||
                     CommandType == TEXT("create_rigid_body_simulation") ||
                     CommandType == TEXT("analyze_chaos_performance"))
            {
                // Convert JSON object to string for Chaos Physics commands
                FString JsonString;
                TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
                FJsonSerializer::Serialize(Params.ToSharedRef(), Writer);

                FString Response;
                if (CommandType == TEXT("create_geometry_collection"))
                {
                    Response = UnrealMCPChaosPhysicsCommands::HandleCreateGeometryCollection(JsonString);
                }
                else if (CommandType == TEXT("fracture_geometry_collection"))
                {
                    Response = UnrealMCPChaosPhysicsCommands::HandleFractureGeometryCollection(JsonString);
                }
                else if (CommandType == TEXT("create_chaos_physics_field"))
                {
                    Response = UnrealMCPChaosPhysicsCommands::HandleCreateChaosPhysicsField(JsonString);
                }
                else if (CommandType == TEXT("spawn_destructible_actor"))
                {
                    Response = UnrealMCPChaosPhysicsCommands::HandleSpawnDestructibleActor(JsonString);
                }
                else if (CommandType == TEXT("apply_chaos_damage"))
                {
                    Response = UnrealMCPChaosPhysicsCommands::HandleApplyChaosDamage(JsonString);
                }
                else if (CommandType == TEXT("create_fluid_simulation"))
                {
                    Response = UnrealMCPChaosPhysicsCommands::HandleCreateFluidSimulation(JsonString);
                }
                else if (CommandType == TEXT("configure_chaos_solver"))
                {
                    Response = UnrealMCPChaosPhysicsCommands::HandleConfigureChaosSolver(JsonString);
                }
                else if (CommandType == TEXT("create_cloth_simulation"))
                {
                    Response = UnrealMCPChaosPhysicsCommands::HandleCreateClothSimulation(JsonString);
                }
                else if (CommandType == TEXT("optimize_chaos_performance"))
                {
                    Response = UnrealMCPChaosPhysicsCommands::HandleOptimizeChaosPerformance(JsonString);
                }
                else if (CommandType == TEXT("create_rigid_body_simulation"))
                {
                    Response = UnrealMCPChaosPhysicsCommands::HandleCreateRigidBodySimulation(JsonString);
                }
                else if (CommandType == TEXT("analyze_chaos_performance"))
                {
                    Response = UnrealMCPChaosPhysicsCommands::HandleAnalyzeChaosPerformance(JsonString);
                }

                // Parse the response JSON
                TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response);
                if (!FJsonSerializer::Deserialize(Reader, ResultJson) || !ResultJson.IsValid())
                {
                    ResultJson = MakeShareable(new FJsonObject);
                    ResultJson->SetBoolField(TEXT("success"), false);
                    ResultJson->SetStringField(TEXT("message"), TEXT("Failed to parse Chaos Physics command response"));
                }
            }
            // Rendering Pipeline Commands
            else if (CommandType == TEXT("configure_lumen_global_illumination") ||
                     CommandType == TEXT("configure_nanite_virtualized_geometry") ||
                     CommandType == TEXT("configure_virtual_shadow_maps") ||
                     CommandType == TEXT("setup_temporal_super_resolution") ||
                     CommandType == TEXT("configure_hardware_ray_tracing") ||
                     CommandType == TEXT("setup_megalights_system") ||
                     CommandType == TEXT("configure_post_process_pipeline") ||
                     CommandType == TEXT("setup_rendering_scalability") ||
                     CommandType == TEXT("optimize_rendering_performance") ||
                     CommandType == TEXT("analyze_rendering_metrics"))
            {
                // Convert JSON object to string for rendering pipeline commands
                FString JsonString;
                TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
                FJsonSerializer::Serialize(Params.ToSharedRef(), Writer);

                FString Response;
                if (CommandType == TEXT("configure_lumen_global_illumination"))
                {
                    Response = UnrealMCPRenderingPipelineCommands::HandleConfigureLumenGlobalIllumination(JsonString);
                }
                else if (CommandType == TEXT("configure_nanite_virtualized_geometry"))
                {
                    Response = UnrealMCPRenderingPipelineCommands::HandleConfigureNaniteVirtualizedGeometry(JsonString);
                }
                else if (CommandType == TEXT("configure_virtual_shadow_maps"))
                {
                    Response = UnrealMCPRenderingPipelineCommands::HandleConfigureVirtualShadowMaps(JsonString);
                }
                else if (CommandType == TEXT("setup_temporal_super_resolution"))
                {
                    Response = UnrealMCPRenderingPipelineCommands::HandleSetupTemporalSuperResolution(JsonString);
                }
                else if (CommandType == TEXT("configure_hardware_ray_tracing"))
                {
                    Response = UnrealMCPRenderingPipelineCommands::HandleConfigureHardwareRayTracing(JsonString);
                }
                else if (CommandType == TEXT("setup_megalights_system"))
                {
                    Response = UnrealMCPRenderingPipelineCommands::HandleSetupMegaLightsSystem(JsonString);
                }
                else if (CommandType == TEXT("configure_post_process_pipeline"))
                {
                    Response = UnrealMCPRenderingPipelineCommands::HandleConfigurePostProcessPipeline(JsonString);
                }
                else if (CommandType == TEXT("setup_rendering_scalability"))
                {
                    Response = UnrealMCPRenderingPipelineCommands::HandleSetupRenderingScalability(JsonString);
                }
                else if (CommandType == TEXT("optimize_rendering_performance"))
                {
                    Response = UnrealMCPRenderingPipelineCommands::HandleOptimizeRenderingPerformance(JsonString);
                }
                else if (CommandType == TEXT("analyze_rendering_metrics"))
                {
                    Response = UnrealMCPRenderingPipelineCommands::HandleAnalyzeRenderingMetrics(JsonString);
                }

                // Parse the response JSON
                TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response);
                if (!FJsonSerializer::Deserialize(Reader, ResultJson) || !ResultJson.IsValid())
                {
                    ResultJson = MakeShareable(new FJsonObject);
                    ResultJson->SetBoolField(TEXT("success"), false);
                    ResultJson->SetStringField(TEXT("message"), TEXT("Failed to parse Rendering Pipeline command response"));
                }
            }
            // Hardware Detection Commands
            else if (CommandType == TEXT("detect_system_hardware") ||
                     CommandType == TEXT("run_hardware_benchmark") ||
                     CommandType == TEXT("detect_gpu_capabilities") ||
                     CommandType == TEXT("analyze_memory_configuration") ||
                     CommandType == TEXT("detect_platform_capabilities") ||
                     CommandType == TEXT("configure_device_profile") ||
                     CommandType == TEXT("optimize_for_hardware_tier") ||
                     CommandType == TEXT("monitor_hardware_performance") ||
                     CommandType == TEXT("generate_hardware_report"))
            {
                // Convert JSON object to string for hardware detection commands
                FString JsonString;
                TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
                FJsonSerializer::Serialize(Params.ToSharedRef(), Writer);

                FString Response;
                if (CommandType == TEXT("detect_system_hardware"))
                {
                    Response = UnrealMCPHardwareDetectionCommands::HandleDetectSystemHardware(JsonString);
                }
                else if (CommandType == TEXT("run_hardware_benchmark"))
                {
                    Response = UnrealMCPHardwareDetectionCommands::HandleRunHardwareBenchmark(JsonString);
                }
                else if (CommandType == TEXT("detect_gpu_capabilities"))
                {
                    Response = UnrealMCPHardwareDetectionCommands::HandleDetectGPUCapabilities(JsonString);
                }
                else if (CommandType == TEXT("analyze_memory_configuration"))
                {
                    Response = UnrealMCPHardwareDetectionCommands::HandleAnalyzeMemoryConfiguration(JsonString);
                }
                else if (CommandType == TEXT("detect_platform_capabilities"))
                {
                    Response = UnrealMCPHardwareDetectionCommands::HandleDetectPlatformCapabilities(JsonString);
                }
                else if (CommandType == TEXT("configure_device_profile"))
                {
                    Response = UnrealMCPHardwareDetectionCommands::HandleConfigureDeviceProfile(JsonString);
                }
                else if (CommandType == TEXT("optimize_for_hardware_tier"))
                {
                    Response = UnrealMCPHardwareDetectionCommands::HandleOptimizeForHardwareTier(JsonString);
                }
                else if (CommandType == TEXT("monitor_hardware_performance"))
                {
                    Response = UnrealMCPHardwareDetectionCommands::HandleMonitorHardwarePerformance(JsonString);
                }
                else if (CommandType == TEXT("generate_hardware_report"))
                {
                    Response = UnrealMCPHardwareDetectionCommands::HandleGenerateHardwareReport(JsonString);
                }

                // Parse the response JSON
                TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response);
                if (!FJsonSerializer::Deserialize(Reader, ResultJson) || !ResultJson.IsValid())
                {
                    ResultJson = MakeShareable(new FJsonObject);
                    ResultJson->SetBoolField(TEXT("success"), false);
                    ResultJson->SetStringField(TEXT("message"), TEXT("Failed to parse Hardware Detection command response"));
                }
            }
            // Audio System Commands
            else if (CommandType == TEXT("create_metasound_source") ||
                     CommandType == TEXT("create_sound_cue") ||
                     CommandType == TEXT("configure_audio_spatialization") ||
                     CommandType == TEXT("setup_audio_submix") ||
                     CommandType == TEXT("create_ambient_sound_actor") ||
                     CommandType == TEXT("configure_audio_volume") ||
                     CommandType == TEXT("setup_procedural_audio") ||
                     CommandType == TEXT("configure_audio_occlusion") ||
                     CommandType == TEXT("manage_audio_streaming") ||
                     CommandType == TEXT("analyze_audio_performance"))
            {
                // Convert JSON object to string for audio system commands
                FString JsonString;
                TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
                FJsonSerializer::Serialize(Params.ToSharedRef(), Writer);

                FString Response;
                if (CommandType == TEXT("create_metasound_source"))
                {
                    Response = UnrealMCPAudioSystemCommands::HandleCreateMetaSoundSource(JsonString);
                }
                else if (CommandType == TEXT("create_sound_cue"))
                {
                    Response = UnrealMCPAudioSystemCommands::HandleCreateSoundCue(JsonString);
                }
                else if (CommandType == TEXT("configure_audio_spatialization"))
                {
                    Response = UnrealMCPAudioSystemCommands::HandleConfigureAudioSpatialization(JsonString);
                }
                else if (CommandType == TEXT("setup_audio_submix"))
                {
                    Response = UnrealMCPAudioSystemCommands::HandleSetupAudioSubmix(JsonString);
                }
                else if (CommandType == TEXT("create_ambient_sound_actor"))
                {
                    Response = UnrealMCPAudioSystemCommands::HandleCreateAmbientSoundActor(JsonString);
                }
                else if (CommandType == TEXT("configure_audio_volume"))
                {
                    Response = UnrealMCPAudioSystemCommands::HandleConfigureAudioVolume(JsonString);
                }
                else if (CommandType == TEXT("setup_procedural_audio"))
                {
                    Response = UnrealMCPAudioSystemCommands::HandleSetupProceduralAudio(JsonString);
                }
                else if (CommandType == TEXT("configure_audio_occlusion"))
                {
                    Response = UnrealMCPAudioSystemCommands::HandleConfigureAudioOcclusion(JsonString);
                }
                else if (CommandType == TEXT("manage_audio_streaming"))
                {
                    Response = UnrealMCPAudioSystemCommands::HandleManageAudioStreaming(JsonString);
                }
                else if (CommandType == TEXT("analyze_audio_performance"))
                {
                    Response = UnrealMCPAudioSystemCommands::HandleAnalyzeAudioPerformance(JsonString);
                }

                // Parse the response JSON
                TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response);
                if (!FJsonSerializer::Deserialize(Reader, ResultJson) || !ResultJson.IsValid())
                {
                    ResultJson = MakeShareable(new FJsonObject);
                    ResultJson->SetBoolField(TEXT("success"), false);
                    ResultJson->SetStringField(TEXT("message"), TEXT("Failed to parse Audio System command response"));
                }
            }
            // Networking Commands
            else if (CommandType == TEXT("create_multiplayer_session") ||
                     CommandType == TEXT("setup_actor_replication") ||
                     CommandType == TEXT("create_rpc_function") ||
                     CommandType == TEXT("configure_network_settings") ||
                     CommandType == TEXT("setup_game_mode_networking") ||
                     CommandType == TEXT("manage_player_connections") ||
                     CommandType == TEXT("configure_replication_graph") ||
                     CommandType == TEXT("setup_network_debugging") ||
                     CommandType == TEXT("analyze_network_performance"))
            {
                // Convert JSON object to string for networking commands
                FString JsonString;
                TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
                FJsonSerializer::Serialize(Params.ToSharedRef(), Writer);

                FString Response;
                if (CommandType == TEXT("create_multiplayer_session"))
                {
                    Response = UnrealMCPNetworkingCommands::HandleCreateMultiplayerSession(JsonString);
                }
                else if (CommandType == TEXT("setup_actor_replication"))
                {
                    Response = UnrealMCPNetworkingCommands::HandleSetupActorReplication(JsonString);
                }
                else if (CommandType == TEXT("create_rpc_function"))
                {
                    Response = UnrealMCPNetworkingCommands::HandleCreateRPCFunction(JsonString);
                }
                else if (CommandType == TEXT("configure_network_settings"))
                {
                    Response = UnrealMCPNetworkingCommands::HandleConfigureNetworkSettings(JsonString);
                }
                else if (CommandType == TEXT("setup_game_mode_networking"))
                {
                    Response = UnrealMCPNetworkingCommands::HandleSetupGameModeNetworking(JsonString);
                }
                else if (CommandType == TEXT("manage_player_connections"))
                {
                    Response = UnrealMCPNetworkingCommands::HandleManagePlayerConnections(JsonString);
                }
                else if (CommandType == TEXT("configure_replication_graph"))
                {
                    Response = UnrealMCPNetworkingCommands::HandleConfigureReplicationGraph(JsonString);
                }
                else if (CommandType == TEXT("setup_network_debugging"))
                {
                    Response = UnrealMCPNetworkingCommands::HandleSetupNetworkDebugging(JsonString);
                }
                else if (CommandType == TEXT("analyze_network_performance"))
                {
                    Response = UnrealMCPNetworkingCommands::HandleAnalyzeNetworkPerformance(JsonString);
                }

                // Parse the response JSON
                TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response);
                if (!FJsonSerializer::Deserialize(Reader, ResultJson) || !ResultJson.IsValid())
                {
                    ResultJson = MakeShareable(new FJsonObject);
                    ResultJson->SetBoolField(TEXT("success"), false);
                    ResultJson->SetStringField(TEXT("message"), TEXT("Failed to parse Networking command response"));
                }
            }
            // Platform Commands
            else if (CommandType == TEXT("configure_platform_settings") ||
                     CommandType == TEXT("setup_mobile_platform") ||
                     CommandType == TEXT("configure_console_platform") ||
                     CommandType == TEXT("setup_cross_platform_features") ||
                     CommandType == TEXT("optimize_platform_performance") ||
                     CommandType == TEXT("configure_platform_input") ||
                     CommandType == TEXT("setup_platform_packaging") ||
                     CommandType == TEXT("manage_platform_store_integration") ||
                     CommandType == TEXT("analyze_platform_compatibility"))
            {
                // Convert JSON object to string for platform commands
                FString JsonString;
                TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
                FJsonSerializer::Serialize(Params.ToSharedRef(), Writer);

                FString Response;
                if (CommandType == TEXT("configure_platform_settings"))
                {
                    Response = UnrealMCPPlatformCommands::HandleConfigurePlatformSettings(JsonString);
                }
                else if (CommandType == TEXT("setup_mobile_platform"))
                {
                    Response = UnrealMCPPlatformCommands::HandleSetupMobilePlatform(JsonString);
                }
                else if (CommandType == TEXT("configure_console_platform"))
                {
                    Response = UnrealMCPPlatformCommands::HandleConfigureConsolePlatform(JsonString);
                }
                else if (CommandType == TEXT("setup_cross_platform_features"))
                {
                    Response = UnrealMCPPlatformCommands::HandleSetupCrossPlatformFeatures(JsonString);
                }
                else if (CommandType == TEXT("optimize_platform_performance"))
                {
                    Response = UnrealMCPPlatformCommands::HandleOptimizePlatformPerformance(JsonString);
                }
                else if (CommandType == TEXT("configure_platform_input"))
                {
                    Response = UnrealMCPPlatformCommands::HandleConfigurePlatformInput(JsonString);
                }
                else if (CommandType == TEXT("setup_platform_packaging"))
                {
                    Response = UnrealMCPPlatformCommands::HandleSetupPlatformPackaging(JsonString);
                }
                else if (CommandType == TEXT("manage_platform_store_integration"))
                {
                    Response = UnrealMCPPlatformCommands::HandleManagePlatformStoreIntegration(JsonString);
                }
                else if (CommandType == TEXT("analyze_platform_compatibility"))
                {
                    Response = UnrealMCPPlatformCommands::HandleAnalyzePlatformCompatibility(JsonString);
                }

                // Parse the response JSON
                TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response);
                if (!FJsonSerializer::Deserialize(Reader, ResultJson) || !ResultJson.IsValid())
                {
                    ResultJson = MakeShareable(new FJsonObject);
                    ResultJson->SetBoolField(TEXT("success"), false);
                    ResultJson->SetStringField(TEXT("message"), TEXT("Failed to parse Platform command response"));
                }
            }
            else
            {
                ResponseJson->SetStringField(TEXT("status"), TEXT("error"));
                ResponseJson->SetStringField(TEXT("error"), FString::Printf(TEXT("Unknown command: %s"), *CommandType));
                
                FString ResultString;
                TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ResultString);
                FJsonSerializer::Serialize(ResponseJson.ToSharedRef(), Writer);
                Promise.SetValue(ResultString);
                return;
            }
            
            // Check if the result contains an error
            bool bSuccess = true;
            FString ErrorMessage;
            
            if (ResultJson->HasField(TEXT("success")))
            {
                bSuccess = ResultJson->GetBoolField(TEXT("success"));
                if (!bSuccess && ResultJson->HasField(TEXT("error")))
                {
                    ErrorMessage = ResultJson->GetStringField(TEXT("error"));
                }
            }
            
            if (bSuccess)
            {
                // Set success status and include the result
                ResponseJson->SetStringField(TEXT("status"), TEXT("success"));
                ResponseJson->SetObjectField(TEXT("result"), ResultJson);
            }
            else
            {
                // Set error status and include the error message
                ResponseJson->SetStringField(TEXT("status"), TEXT("error"));
                ResponseJson->SetStringField(TEXT("error"), ErrorMessage);
            }
        }
        catch (const std::exception& e)
        {
            ResponseJson->SetStringField(TEXT("status"), TEXT("error"));
            ResponseJson->SetStringField(TEXT("error"), UTF8_TO_TCHAR(e.what()));
        }
        
        FString ResultString;
        TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ResultString);
        FJsonSerializer::Serialize(ResponseJson.ToSharedRef(), Writer);
        Promise.SetValue(ResultString);
    });
    
    return Future.Get();
}