// Copyright Epic Games, Inc. All Rights Reserved.

#include "Commands/UnrealMCPChaosPhysicsCommands.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "Engine/World.h"
#include "Engine/StaticMesh.h"
#include "GeometryCollection/GeometryCollectionEngineUtility.h"
#include "GeometryCollection/GeometryCollectionAlgo.h"
#include "Field/FieldSystemNodes.h"
#include "Field/FieldSystemAsset.h"
#include "UObject/SavePackage.h"
#include "GeometryCollection/GeometryCollectionActor.h"
#include "GeometryCollection/GeometryCollectionComponent.h"
#include "Engine/StaticMeshActor.h"
#include "Kismet/GameplayStatics.h"

#include "NiagaraFunctionLibrary.h"
#include "HAL/IConsoleManager.h"
#include "Chaos/ChaosGameplayEventDispatcher.h"
#include "AssetToolsModule.h"
#include "IAssetTools.h"
#include "PackageTools.h"
#include "FileHelpers.h"

// Initialize static constants
const TArray<FString> UnrealMCPChaosPhysicsCommands::SupportedFractureMethods = {
    TEXT("Voronoi"),
    TEXT("Uniform"),
    TEXT("Clustered"),
    TEXT("Radial"),
    TEXT("Planar"),
    TEXT("Brick"),
    TEXT("Custom")
};

const TArray<FString> UnrealMCPChaosPhysicsCommands::SupportedFieldTypes = {
    TEXT("RadialForce"),
    TEXT("DirectionalForce"),
    TEXT("NoiseField"),
    TEXT("UniformVector"),
    TEXT("RadialVector"),
    TEXT("RandomVector"),
    TEXT("CurlNoise"),
    TEXT("GravityField"),
    TEXT("MagneticField")
};

const TArray<FString> UnrealMCPChaosPhysicsCommands::SupportedFluidTypes = {
    TEXT("Water"),
    TEXT("Oil"),
    TEXT("Gas"),
    TEXT("Lava"),
    TEXT("Acid"),
    TEXT("Custom")
};

const TArray<FString> UnrealMCPChaosPhysicsCommands::SupportedOptimizationLevels = {
    TEXT("Low"),
    TEXT("Medium"),
    TEXT("High"),
    TEXT("Extreme")
};

const TArray<FString> UnrealMCPChaosPhysicsCommands::SupportedPlatforms = {
    TEXT("Windows"),
    TEXT("Mac"),
    TEXT("Linux"),
    TEXT("Android"),
    TEXT("iOS"),
    TEXT("PlayStation5"),
    TEXT("XboxSeriesX"),
    TEXT("Switch")
};

const TArray<FString> UnrealMCPChaosPhysicsCommands::SupportedOutputFormats = {
    TEXT("JSON"),
    TEXT("CSV"),
    TEXT("XML")
};

UnrealMCPChaosPhysicsCommands::UnrealMCPChaosPhysicsCommands()
{
}

UnrealMCPChaosPhysicsCommands::~UnrealMCPChaosPhysicsCommands()
{
}

FString UnrealMCPChaosPhysicsCommands::HandleCreateGeometryCollection(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject = ParseJsonParams(JsonParams);
    if (!JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString CollectionName;
    if (!JsonObject->TryGetStringField(TEXT("collection_name"), CollectionName) || CollectionName.IsEmpty())
    {
        return CreateJsonResponse(false, TEXT("Missing or empty collection_name parameter"));
    }

    FString CollectionPath = TEXT("/Game/Physics/GeometryCollections");
    JsonObject->TryGetStringField(TEXT("collection_path"), CollectionPath);

    FString SourceMeshes;
    JsonObject->TryGetStringField(TEXT("source_meshes"), SourceMeshes);

    bool bAutoCluster = true;
    JsonObject->TryGetBoolField(TEXT("auto_cluster"), bAutoCluster);

    // Validate parameters
    if (!ValidateChaosPath(CollectionPath))
    {
        return CreateJsonResponse(false, TEXT("Invalid collection path"));
    }

    // Create the geometry collection
    UGeometryCollection* NewCollection = CreateGeometryCollectionAsset(CollectionName, CollectionPath);
    if (!NewCollection)
    {
        return CreateJsonResponse(false, TEXT("Failed to create geometry collection asset"));
    }

    // Add source meshes if provided
    if (!SourceMeshes.IsEmpty())
    {
        TArray<FString> MeshPaths;
        SourceMeshes.ParseIntoArray(MeshPaths, TEXT(","), true);
        
        if (!AddMeshesToGeometryCollection(NewCollection, MeshPaths))
        {
            return CreateJsonResponse(false, TEXT("Failed to add meshes to geometry collection"));
        }
    }

    // Configure clustering
    if (bAutoCluster)
    {
        // Note: Clustering is handled differently in UE 5.6 - use the Fracture Editor tools
    }

    // Save the collection
    FString PackageName = ChaosPathToPackageName(CollectionPath + TEXT("/") + CollectionName);
    if (!SaveChaosAsset(NewCollection, PackageName))
    {
        return CreateJsonResponse(false, TEXT("Failed to save geometry collection asset"));
    }

    TMap<FString, FString> ResponseData;
    ResponseData.Add(TEXT("collection_path"), PackageName);
    ResponseData.Add(TEXT("collection_name"), CollectionName);
    ResponseData.Add(TEXT("auto_cluster"), bAutoCluster ? TEXT("true") : TEXT("false"));

    return CreateJsonResponse(true, TEXT("Geometry collection created successfully"), ResponseData);
}

FString UnrealMCPChaosPhysicsCommands::HandleFractureGeometryCollection(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject = ParseJsonParams(JsonParams);
    if (!JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString CollectionPath;
    if (!JsonObject->TryGetStringField(TEXT("collection_path"), CollectionPath) || CollectionPath.IsEmpty())
    {
        return CreateJsonResponse(false, TEXT("Missing or empty collection_path parameter"));
    }

    FString FractureMethod = TEXT("Voronoi");
    JsonObject->TryGetStringField(TEXT("fracture_method"), FractureMethod);

    int32 FractureCount = 10;
    JsonObject->TryGetNumberField(TEXT("fracture_count"), FractureCount);

    int32 RandomSeed = 42;
    JsonObject->TryGetNumberField(TEXT("random_seed"), RandomSeed);

    // Validate parameters
    if (!ValidateFractureMethod(FractureMethod))
    {
        return CreateJsonResponse(false, FString::Printf(TEXT("Unsupported fracture method: %s"), *FractureMethod));
    }

    // Load geometry collection
    UGeometryCollection* Collection = LoadGeometryCollectionFromPath(CollectionPath);
    if (!Collection)
    {
        return CreateJsonResponse(false, TEXT("Failed to load geometry collection"));
    }

    // Perform fracturing
    if (!FractureGeometryCollectionInternal(Collection, FractureMethod, FractureCount, RandomSeed))
    {
        return CreateJsonResponse(false, TEXT("Failed to fracture geometry collection"));
    }

    // Mark collection as modified
    Collection->MarkPackageDirty();

    TMap<FString, FString> ResponseData;
    ResponseData.Add(TEXT("collection_path"), CollectionPath);
    ResponseData.Add(TEXT("fracture_method"), FractureMethod);
    ResponseData.Add(TEXT("fracture_count"), FString::FromInt(FractureCount));
    ResponseData.Add(TEXT("random_seed"), FString::FromInt(RandomSeed));

    return CreateJsonResponse(true, TEXT("Geometry collection fractured successfully"), ResponseData);
}

FString UnrealMCPChaosPhysicsCommands::HandleCreateChaosPhysicsField(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject = ParseJsonParams(JsonParams);
    if (!JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString FieldName;
    if (!JsonObject->TryGetStringField(TEXT("field_name"), FieldName) || FieldName.IsEmpty())
    {
        return CreateJsonResponse(false, TEXT("Missing or empty field_name parameter"));
    }

    FString FieldType;
    if (!JsonObject->TryGetStringField(TEXT("field_type"), FieldType) || FieldType.IsEmpty())
    {
        return CreateJsonResponse(false, TEXT("Missing or empty field_type parameter"));
    }

    FString FieldLocation = TEXT("0,0,0");
    JsonObject->TryGetStringField(TEXT("field_location"), FieldLocation);

    double FieldRadius = 500.0;
    JsonObject->TryGetNumberField(TEXT("field_radius"), FieldRadius);

    double FieldStrength = 1000.0;
    JsonObject->TryGetNumberField(TEXT("field_strength"), FieldStrength);

    // Validate parameters
    if (!ValidateFieldType(FieldType))
    {
        return CreateJsonResponse(false, FString::Printf(TEXT("Unsupported field type: %s"), *FieldType));
    }

    // Parse location
    TArray<FString> LocationComponents;
    FieldLocation.ParseIntoArray(LocationComponents, TEXT(","), true);
    FVector Location = FVector::ZeroVector;
    if (LocationComponents.Num() >= 3)
    {
        Location.X = FCString::Atof(*LocationComponents[0]);
        Location.Y = FCString::Atof(*LocationComponents[1]);
        Location.Z = FCString::Atof(*LocationComponents[2]);
    }

    // Create physics field actor
    AFieldSystemActor* FieldActor = CreatePhysicsFieldActor(FieldName, FieldType, Location);
    if (!FieldActor)
    {
        return CreateJsonResponse(false, TEXT("Failed to create physics field actor"));
    }

    // Setup field component
    UFieldSystemComponent* FieldComponent = SetupFieldComponent(FieldActor, FieldType, 
        static_cast<float>(FieldRadius), static_cast<float>(FieldStrength));
    if (!FieldComponent)
    {
        return CreateJsonResponse(false, TEXT("Failed to setup field component"));
    }

    TMap<FString, FString> ResponseData;
    ResponseData.Add(TEXT("field_name"), FieldName);
    ResponseData.Add(TEXT("field_type"), FieldType);
    ResponseData.Add(TEXT("field_location"), FieldLocation);
    ResponseData.Add(TEXT("field_radius"), FString::SanitizeFloat(FieldRadius));
    ResponseData.Add(TEXT("field_strength"), FString::SanitizeFloat(FieldStrength));

    return CreateJsonResponse(true, TEXT("Chaos physics field created successfully"), ResponseData);
}

// Helper function implementations
UGeometryCollection* UnrealMCPChaosPhysicsCommands::CreateGeometryCollectionAsset(
    const FString& CollectionName, const FString& PackagePath)
{
    FString PackageName = ChaosPathToPackageName(PackagePath + TEXT("/") + CollectionName);
    UPackage* Package = CreatePackage(*PackageName);
    if (!Package)
    {
        return nullptr;
    }

    UGeometryCollectionFactory* CollectionFactory = NewObject<UGeometryCollectionFactory>();
    UGeometryCollection* NewCollection = Cast<UGeometryCollection>(CollectionFactory->FactoryCreateNew(
        UGeometryCollection::StaticClass(), Package, FName(*CollectionName), RF_Standalone | RF_Public, nullptr, GWarn));

    if (NewCollection)
    {
        FAssetRegistryModule::AssetCreated(NewCollection);
        Package->MarkPackageDirty();
    }

    return NewCollection;
}

bool UnrealMCPChaosPhysicsCommands::AddMeshesToGeometryCollection(UGeometryCollection* Collection,
    const TArray<FString>& MeshPaths)
{
    if (!Collection)
    {
        return false;
    }

    for (const FString& MeshPath : MeshPaths)
    {
        UStaticMesh* StaticMesh = LoadObject<UStaticMesh>(nullptr, *MeshPath);
        if (StaticMesh)
        {
            // Add mesh to geometry collection
            // Note: Static mesh appending is handled differently in UE 5.6
        }
    }

    return true;
}

bool UnrealMCPChaosPhysicsCommands::FractureGeometryCollectionInternal(UGeometryCollection* Collection,
    const FString& FractureMethod, int32 FractureCount, int32 RandomSeed)
{
    if (!Collection)
    {
        return false;
    }

    // Set random seed
    FMath::RandInit(RandomSeed);

    // Apply fracturing based on method
    // Note: Fracture algorithms are handled through the Fracture Editor tools in UE 5.6
    // Use the FractureEditor plugin for advanced fracturing operations

    return true;
}

AFieldSystemActor* UnrealMCPChaosPhysicsCommands::CreatePhysicsFieldActor(const FString& FieldName,
    const FString& FieldType, const FVector& Location)
{
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        return nullptr;
    }

    // Spawn field system actor
    AFieldSystemActor* FieldActor = World->SpawnActor<AFieldSystemActor>();
    if (FieldActor)
    {
        FieldActor->SetActorLocation(Location);
        FieldActor->SetActorLabel(FieldName);
    }

    return FieldActor;
}

UFieldSystemComponent* UnrealMCPChaosPhysicsCommands::SetupFieldComponent(AFieldSystemActor* FieldActor,
    const FString& FieldType, float Radius, float Strength)
{
    if (!FieldActor)
    {
        return nullptr;
    }

    UFieldSystemComponent* FieldComponent = FieldActor->GetFieldSystemComponent();
    if (!FieldComponent)
    {
        return nullptr;
    }

    // Configure field based on type
    if (FieldType == TEXT("RadialForce"))
    {
        ConfigureRadialForceField(FieldComponent, Radius, Strength);
    }
    else if (FieldType == TEXT("DirectionalForce"))
    {
        ConfigureDirectionalForceField(FieldComponent, FVector(0, 0, 1), Strength);
    }

    return FieldComponent;
}

bool UnrealMCPChaosPhysicsCommands::ConfigureRadialForceField(UFieldSystemComponent* FieldComponent,
    float Radius, float Strength)
{
    if (!FieldComponent)
    {
        return false;
    }

    // Create field system asset
    UFieldSystem* FieldSystem = NewObject<UFieldSystem>(FieldComponent);

    // Create radial force field nodes
    URadialVector* RadialVector = NewObject<URadialVector>(FieldSystem);
    RadialVector->Magnitude = Strength;
    RadialVector->Position = FVector::ZeroVector;

    URadialFalloff* SphereField = NewObject<URadialFalloff>(FieldSystem);
    SphereField->Radius = Radius;
    SphereField->Position = FVector::ZeroVector;

    // Create a multiply node to combine the vector field with the falloff
    UFieldNodeFloat* CombinedField = NewObject<UFieldNodeFloat>(FieldSystem);

    // Set up the field system
    FieldComponent->SetFieldSystem(FieldSystem);

    return true;
}

TSharedPtr<FJsonObject> UnrealMCPChaosPhysicsCommands::ParseJsonParams(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonParams);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return nullptr;
    }

    return JsonObject;
}

FString UnrealMCPChaosPhysicsCommands::CreateJsonResponse(bool bSuccess, const FString& Message,
    const TMap<FString, FString>& AdditionalData)
{
    TSharedPtr<FJsonObject> ResponseObject = MakeShareable(new FJsonObject);
    ResponseObject->SetBoolField(TEXT("success"), bSuccess);
    ResponseObject->SetStringField(TEXT("message"), Message);

    if (AdditionalData.Num() > 0)
    {
        TSharedPtr<FJsonObject> DataObject = MakeShareable(new FJsonObject);
        for (const auto& Pair : AdditionalData)
        {
            DataObject->SetStringField(Pair.Key, Pair.Value);
        }
        ResponseObject->SetObjectField(TEXT("data"), DataObject);
    }

    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(ResponseObject.ToSharedRef(), Writer);

    return OutputString;
}

FString UnrealMCPChaosPhysicsCommands::ChaosPathToPackageName(const FString& ChaosPath)
{
    FString PackageName = ChaosPath;
    if (!PackageName.StartsWith(TEXT("/Game/")))
    {
        PackageName = TEXT("/Game/") + PackageName;
    }
    return PackageName;
}

bool UnrealMCPChaosPhysicsCommands::ValidateChaosPath(const FString& ChaosPath)
{
    return !ChaosPath.IsEmpty() && (ChaosPath.StartsWith(TEXT("/Game/")) || ChaosPath.StartsWith(TEXT("/Engine/")));
}

bool UnrealMCPChaosPhysicsCommands::ValidateFractureMethod(const FString& FractureMethod)
{
    return SupportedFractureMethods.Contains(FractureMethod);
}

bool UnrealMCPChaosPhysicsCommands::ValidateFieldType(const FString& FieldType)
{
    return SupportedFieldTypes.Contains(FieldType);
}

// Private helper function implementations
bool UnrealMCPChaosPhysicsCommands::ConfigureDirectionalForceField(UFieldSystemComponent* FieldComponent, const FVector& Direction, float Strength)
{
    if (!FieldComponent)
    {
        return false;
    }

    // Create a uniform vector field for directional force
    UUniformVector* DirectionalField = NewObject<UUniformVector>(FieldComponent);
    DirectionalField->Direction = Direction.GetSafeNormal();
    DirectionalField->Magnitude = Strength;

    return true;
}

UGeometryCollection* UnrealMCPChaosPhysicsCommands::LoadGeometryCollectionFromPath(const FString& AssetPath)
{
    if (AssetPath.IsEmpty())
    {
        return nullptr;
    }

    // Load the geometry collection asset from the given path
    UGeometryCollection* Collection = LoadObject<UGeometryCollection>(nullptr, *AssetPath);
    return Collection;
}

bool UnrealMCPChaosPhysicsCommands::SaveChaosAsset(UObject* Asset, const FString& PackagePath)
{
    if (!Asset || PackagePath.IsEmpty())
    {
        return false;
    }

    // Create package and save the asset
    UPackage* Package = CreatePackage(*PackagePath);
    if (!Package)
    {
        return false;
    }

    Asset->Rename(nullptr, Package);
    Package->MarkPackageDirty();

    FSavePackageArgs SaveArgs;
    SaveArgs.TopLevelFlags = RF_Public | RF_Standalone;
    return UPackage::SavePackage(Package, Asset, *FPackageName::LongPackageNameToFilename(PackagePath, FPackageName::GetAssetPackageExtension()), SaveArgs);
}

FString UnrealMCPChaosPhysicsCommands::HandleSpawnDestructibleActor(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    FString GeometryCollectionPath = JsonObject->GetStringField(TEXT("geometry_collection_path"));

    const TSharedPtr<FJsonObject>* LocationObj;
    if (!JsonObject->TryGetObjectField(TEXT("location"), LocationObj))
    {
        return TEXT("{\"success\": false, \"error\": \"Missing location parameter\"}");
    }

    FVector Location(
        (*LocationObj)->GetNumberField(TEXT("x")),
        (*LocationObj)->GetNumberField(TEXT("y")),
        (*LocationObj)->GetNumberField(TEXT("z"))
    );

    UGeometryCollection* GeometryCollection = LoadGeometryCollectionFromPath(GeometryCollectionPath);
    if (!GeometryCollection)
    {
        return TEXT("{\"success\": false, \"error\": \"Geometry collection not found\"}");
    }

    // Get current world
    UWorld* World = GEngine->GetWorldFromContextObject(nullptr, EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        return TEXT("{\"success\": false, \"error\": \"No valid world found\"}");
    }

    // Spawn geometry collection actor
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    AGeometryCollectionActor* DestructibleActor = World->SpawnActor<AGeometryCollectionActor>(AGeometryCollectionActor::StaticClass(), Location, FRotator::ZeroRotator, SpawnParams);
    if (!DestructibleActor)
    {
        return TEXT("{\"success\": false, \"error\": \"Failed to spawn destructible actor\"}");
    }

    // Set the geometry collection
    DestructibleActor->GetGeometryCollectionComponent()->SetRestCollection(GeometryCollection);

    return TEXT("{\"success\": true, \"message\": \"Destructible actor spawned successfully\"}");
}

FString UnrealMCPChaosPhysicsCommands::HandleApplyChaosDamage(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    const TSharedPtr<FJsonObject>* LocationObj;
    if (!JsonObject->TryGetObjectField(TEXT("damage_location"), LocationObj))
    {
        return TEXT("{\"success\": false, \"error\": \"Missing damage_location parameter\"}");
    }

    FVector DamageLocation(
        (*LocationObj)->GetNumberField(TEXT("x")),
        (*LocationObj)->GetNumberField(TEXT("y")),
        (*LocationObj)->GetNumberField(TEXT("z"))
    );

    double DamageRadius = JsonObject->GetNumberField(TEXT("damage_radius"));
    double DamageStrength = JsonObject->GetNumberField(TEXT("damage_strength"));

    // Get current world
    UWorld* World = GEngine->GetWorldFromContextObject(nullptr, EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        return TEXT("{\"success\": false, \"error\": \"No valid world found\"}");
    }

    // Find all geometry collection actors in range
    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(World, AGeometryCollectionActor::StaticClass(), FoundActors);

    int32 ActorsAffected = 0;
    for (AActor* Actor : FoundActors)
    {
        AGeometryCollectionActor* GCActor = Cast<AGeometryCollectionActor>(Actor);
        if (GCActor && FVector::Dist(GCActor->GetActorLocation(), DamageLocation) <= DamageRadius)
        {
            // Apply damage to geometry collection
            UGeometryCollectionComponent* GCComponent = GCActor->GetGeometryCollectionComponent();
            if (GCComponent)
            {
                // Apply radial damage using correct enum
                GCComponent->ApplyPhysicsField(true, EGeometryCollectionPhysicsTypeEnum::Chaos_DynamicState, nullptr, nullptr);
                ActorsAffected++;
            }
        }
    }

    return FString::Printf(TEXT("{\"success\": true, \"message\": \"Damage applied to %d actors\"}"), ActorsAffected);
}

FString UnrealMCPChaosPhysicsCommands::HandleConfigureChaosSolver(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    double TimeStep = JsonObject->GetNumberField(TEXT("time_step"));
    int32 Iterations = static_cast<int32>(JsonObject->GetNumberField(TEXT("iterations")));
    bool bEnableReplication = JsonObject->GetBoolField(TEXT("enable_replication"));

    // Get current world
    UWorld* World = GEngine->GetWorldFromContextObject(nullptr, EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        return TEXT("{\"success\": false, \"error\": \"No valid world found\"}");
    }

    // Configure Chaos solver settings
    // Note: Chaos solver configuration is handled differently in UE 5.6

    return TEXT("{\"success\": true, \"message\": \"Chaos solver configured successfully\"}");
}

FString UnrealMCPChaosPhysicsCommands::HandleCreateFluidSimulation(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    FString FluidName = JsonObject->GetStringField(TEXT("fluid_name"));

    const TSharedPtr<FJsonObject>* LocationObj;
    if (!JsonObject->TryGetObjectField(TEXT("location"), LocationObj))
    {
        return TEXT("{\"success\": false, \"error\": \"Missing location parameter\"}");
    }

    FVector Location(
        (*LocationObj)->GetNumberField(TEXT("x")),
        (*LocationObj)->GetNumberField(TEXT("y")),
        (*LocationObj)->GetNumberField(TEXT("z"))
    );

    // Get current world
    UWorld* World = GEngine->GetWorldFromContextObject(nullptr, EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        return TEXT("{\"success\": false, \"error\": \"No valid world found\"}");
    }

    // Create fluid simulation using Niagara (as UE5.6 uses Niagara for fluid effects)
    UNiagaraSystem* FluidSystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Engine/VFX/Niagara/Systems/NS_FluidSimulation"));
    if (!FluidSystem)
    {
        return TEXT("{\"success\": false, \"error\": \"Fluid simulation system not found\"}");
    }

    // Spawn fluid system
    UNiagaraComponent* FluidComponent = UNiagaraFunctionLibrary::SpawnSystemAtLocation(World, FluidSystem, Location);
    if (!FluidComponent)
    {
        return TEXT("{\"success\": false, \"error\": \"Failed to spawn fluid simulation\"}");
    }

    return TEXT("{\"success\": true, \"message\": \"Fluid simulation created successfully\"}");
}

FString UnrealMCPChaosPhysicsCommands::HandleCreateClothSimulation(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    FString ClothName = JsonObject->GetStringField(TEXT("cloth_name"));
    FString MeshPath = JsonObject->GetStringField(TEXT("mesh_path"));

    UStaticMesh* ClothMesh = LoadObject<UStaticMesh>(nullptr, *MeshPath);
    if (!ClothMesh)
    {
        return TEXT("{\"success\": false, \"error\": \"Cloth mesh not found\"}");
    }

    // Get current world
    UWorld* World = GEngine->GetWorldFromContextObject(nullptr, EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        return TEXT("{\"success\": false, \"error\": \"No valid world found\"}");
    }

    // Create cloth actor with Chaos cloth simulation
    FActorSpawnParameters SpawnParams;
    AStaticMeshActor* ClothActor = World->SpawnActor<AStaticMeshActor>(AStaticMeshActor::StaticClass(), SpawnParams);
    if (!ClothActor)
    {
        return TEXT("{\"success\": false, \"error\": \"Failed to create cloth actor\"}");
    }

    // Set up the mesh
    ClothActor->GetStaticMeshComponent()->SetStaticMesh(ClothMesh);
    ClothActor->GetStaticMeshComponent()->SetSimulatePhysics(true);

    return TEXT("{\"success\": true, \"message\": \"Cloth simulation created successfully\"}");
}

FString UnrealMCPChaosPhysicsCommands::HandleCreateRigidBodySimulation(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    FString RigidBodyName = JsonObject->GetStringField(TEXT("rigid_body_name"));
    FString MeshPath = JsonObject->GetStringField(TEXT("mesh_path"));
    double Mass = JsonObject->GetNumberField(TEXT("mass"));

    const TSharedPtr<FJsonObject>* LocationObj;
    if (!JsonObject->TryGetObjectField(TEXT("location"), LocationObj))
    {
        return TEXT("{\"success\": false, \"error\": \"Missing location parameter\"}");
    }

    FVector Location(
        (*LocationObj)->GetNumberField(TEXT("x")),
        (*LocationObj)->GetNumberField(TEXT("y")),
        (*LocationObj)->GetNumberField(TEXT("z"))
    );

    UStaticMesh* RigidBodyMesh = LoadObject<UStaticMesh>(nullptr, *MeshPath);
    if (!RigidBodyMesh)
    {
        return TEXT("{\"success\": false, \"error\": \"Rigid body mesh not found\"}");
    }

    // Get current world
    UWorld* World = GEngine->GetWorldFromContextObject(nullptr, EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        return TEXT("{\"success\": false, \"error\": \"No valid world found\"}");
    }

    // Create rigid body actor
    FActorSpawnParameters SpawnParams;
    AStaticMeshActor* RigidBodyActor = World->SpawnActor<AStaticMeshActor>(AStaticMeshActor::StaticClass(), Location, FRotator::ZeroRotator, SpawnParams);
    if (!RigidBodyActor)
    {
        return TEXT("{\"success\": false, \"error\": \"Failed to create rigid body actor\"}");
    }

    // Configure rigid body physics
    UStaticMeshComponent* MeshComponent = RigidBodyActor->GetStaticMeshComponent();
    MeshComponent->SetStaticMesh(RigidBodyMesh);
    MeshComponent->SetSimulatePhysics(true);
    MeshComponent->SetMassOverrideInKg(NAME_None, static_cast<float>(Mass));
    MeshComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    MeshComponent->SetCollisionResponseToAllChannels(ECR_Block);

    return TEXT("{\"success\": true, \"message\": \"Rigid body simulation created successfully\"}");
}

FString UnrealMCPChaosPhysicsCommands::HandleOptimizeChaosPerformance(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    FString OptimizationLevel = JsonObject->GetStringField(TEXT("optimization_level"));

    // Apply Chaos physics optimizations
    if (OptimizationLevel == TEXT("Low"))
    {
        // Basic optimizations
        IConsoleVariable* ChaosThreads = IConsoleManager::Get().FindConsoleVariable(TEXT("p.Chaos.Solver.AsyncDt"));
        if (ChaosThreads)
        {
            ChaosThreads->Set(0.016f); // 60 FPS
        }
    }
    else if (OptimizationLevel == TEXT("Medium"))
    {
        // Moderate optimizations
        IConsoleVariable* ChaosThreads = IConsoleManager::Get().FindConsoleVariable(TEXT("p.Chaos.Solver.AsyncDt"));
        if (ChaosThreads)
        {
            ChaosThreads->Set(0.033f); // 30 FPS
        }
    }
    else if (OptimizationLevel == TEXT("High"))
    {
        // Aggressive optimizations
        IConsoleVariable* ChaosThreads = IConsoleManager::Get().FindConsoleVariable(TEXT("p.Chaos.Solver.AsyncDt"));
        if (ChaosThreads)
        {
            ChaosThreads->Set(0.05f); // 20 FPS
        }
    }

    return TEXT("{\"success\": true, \"message\": \"Chaos physics performance optimized\"}");
}

FString UnrealMCPChaosPhysicsCommands::HandleAnalyzeChaosPerformance(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    // Get current world
    UWorld* World = GEngine->GetWorldFromContextObject(nullptr, EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        return TEXT("{\"success\": false, \"error\": \"No valid world found\"}");
    }

    // Analyze Chaos physics performance
    TSharedPtr<FJsonObject> AnalysisResult = MakeShareable(new FJsonObject);

    // Count physics objects
    TArray<AActor*> PhysicsActors;
    UGameplayStatics::GetAllActorsOfClass(World, AStaticMeshActor::StaticClass(), PhysicsActors);

    int32 SimulatingActors = 0;
    for (AActor* Actor : PhysicsActors)
    {
        if (AStaticMeshActor* MeshActor = Cast<AStaticMeshActor>(Actor))
        {
            if (MeshActor->GetStaticMeshComponent()->IsSimulatingPhysics())
            {
                SimulatingActors++;
            }
        }
    }

    AnalysisResult->SetNumberField(TEXT("total_physics_actors"), PhysicsActors.Num());
    AnalysisResult->SetNumberField(TEXT("simulating_actors"), SimulatingActors);

    // Performance recommendations
    TArray<TSharedPtr<FJsonValue>> Recommendations;
    if (SimulatingActors > 100)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("Consider reducing number of simulating physics objects"))));
    }

    AnalysisResult->SetArrayField(TEXT("recommendations"), Recommendations);

    // Serialize result
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);

    TSharedPtr<FJsonObject> ResponseObject = MakeShareable(new FJsonObject);
    ResponseObject->SetBoolField(TEXT("success"), true);
    ResponseObject->SetObjectField(TEXT("analysis"), AnalysisResult);

    FJsonSerializer::Serialize(ResponseObject.ToSharedRef(), Writer);

    return OutputString;
}
